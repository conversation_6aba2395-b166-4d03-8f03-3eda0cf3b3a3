import 'package:flutter/services.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:pdf/pdf.dart';
import 'package:intl/intl.dart';
import '../models/user_model.dart';
import '../models/department_model.dart';
import '../services/api/task_api_service.dart';
import '../services/api/user_api_service.dart';
import '../services/api/departments_api_service.dart';

/// دالة توليد تقرير المهام الشامل والاحترافي
Future<pw.Document> generateTaskFullPdfReport({List<String>? userIds, List<String>? departmentIds, List<String>? statuses, DateTime? dueDateFrom, DateTime? dueDateTo}) async {
  final pdf = pw.Document();
  // تحميل الخطوط العربية
  final fontData = await rootBundle.load('assets/fonts/NotoNaskhArabic-Regular.ttf');
  final boldFontData = await rootBundle.load('assets/fonts/NotoNaskhArabic-Bold.ttf');
  final arabicFont = pw.Font.ttf(fontData);
  final arabicFontBold = pw.Font.ttf(boldFontData);

  // جلب جميع المهام
  final allTasks = await TaskApiService().getAllTasks(forceRefresh: true);
  final allUsers = await UserApiService().getAllUsers();
  final allDepartments = await DepartmentsApiService().getAllDepartments();

  // فلترة المهام حسب الفلاتر
  final tasks = allTasks.where((task) {
    final userMatch = userIds == null || userIds.isEmpty || userIds.contains(task.assigneeId?.toString());
    final deptMatch = departmentIds == null || departmentIds.isEmpty || departmentIds.contains(task.departmentId?.toString());
    final statusMatch = statuses == null || statuses.isEmpty || statuses.contains(task.status);
    // فلترة حسب تاريخ الاستحقاق (مقارنة التاريخ فقط بدون الوقت)
    bool dueDateMatch = true;
    if (dueDateFrom != null || dueDateTo != null) {
      if (task.dueDateDateTime == null) return false;
      final taskDueDate = DateTime(task.dueDateDateTime!.year, task.dueDateDateTime!.month, task.dueDateDateTime!.day);
      if (dueDateFrom != null) {
        final fromDate = DateTime(dueDateFrom.year, dueDateFrom.month, dueDateFrom.day);
        if (taskDueDate.isBefore(fromDate)) dueDateMatch = false;
      }
      if (dueDateTo != null) {
        final toDate = DateTime(dueDateTo.year, dueDateTo.month, dueDateTo.day);
        if (taskDueDate.isAfter(toDate)) dueDateMatch = false;
      }
    }
    return userMatch && deptMatch && statusMatch && dueDateMatch;
  }).toList();

  // بناء التقرير
  pdf.addPage(
    pw.MultiPage(
      pageFormat: PdfPageFormat.a4,
      textDirection: pw.TextDirection.rtl,
      margin: const pw.EdgeInsets.symmetric(horizontal: 16, vertical: 20),
      build: (context) => [
        pw.Center(
          child: pw.Text(
            'تقرير المهام الشامل',
            style: pw.TextStyle(
                font: arabicFontBold,
                fontSize: 20,
                color: PdfColors.blue900,
                fontWeight: pw.FontWeight.bold),
          ),
        ),
        pw.SizedBox(height: 8),
        pw.Container(
          padding: const pw.EdgeInsets.symmetric(vertical: 6, horizontal: 18),
          decoration: pw.BoxDecoration(
            color: PdfColors.blue50,
            borderRadius: pw.BorderRadius.circular(8),
            border: pw.Border.all(color: PdfColors.blue200, width: 1),
          ),
          child: pw.Text(
            'عدد المهام: ${tasks.length}',
            style: pw.TextStyle(font: arabicFontBold, fontSize: 14, color: PdfColors.blue900),
          ),
        ),
        pw.SizedBox(height: 12),
        pw.Table(
          border: pw.TableBorder.all(color: PdfColors.blueGrey, width: 0.7),
          columnWidths: {
            0: const pw.FlexColumnWidth(3), // العنوان
            1: const pw.FlexColumnWidth(2), // الحالة
            2: const pw.FlexColumnWidth(2), // القسم
            3: const pw.FlexColumnWidth(2), // المسؤول
            4: const pw.FlexColumnWidth(2), // تاريخ البداية
            5: const pw.FlexColumnWidth(2), // تاريخ النهاية
          },
          children: [
            pw.TableRow(
              decoration: const pw.BoxDecoration(color: PdfColors.grey300),
              children: [
                pw.Padding(
                  padding: const pw.EdgeInsets.all(4),
                  child: pw.Text('العنوان', style: pw.TextStyle(font: arabicFontBold, fontWeight: pw.FontWeight.bold)),
                ),
                pw.Padding(
                  padding: const pw.EdgeInsets.all(4),
                  child: pw.Text('الحالة', style: pw.TextStyle(font: arabicFontBold, fontWeight: pw.FontWeight.bold)),
                ),
                pw.Padding(
                  padding: const pw.EdgeInsets.all(4),
                  child: pw.Text('القسم', style: pw.TextStyle(font: arabicFontBold, fontWeight: pw.FontWeight.bold)),
                ),
                pw.Padding(
                  padding: const pw.EdgeInsets.all(4),
                  child: pw.Text('المسؤول', style: pw.TextStyle(font: arabicFontBold, fontWeight: pw.FontWeight.bold)),
                ),
                pw.Padding(
                  padding: const pw.EdgeInsets.all(4),
                  child: pw.Text('تاريخ البداية', style: pw.TextStyle(font: arabicFontBold, fontWeight: pw.FontWeight.bold)),
                ),
                pw.Padding(
                  padding: const pw.EdgeInsets.all(4),
                  child: pw.Text('تاريخ النهاية', style: pw.TextStyle(font: arabicFontBold, fontWeight: pw.FontWeight.bold)),
                ),
              ],
            ),
            ...tasks.map((task) {
              final user = allUsers.firstWhere((u) => u.id == task.assigneeId, orElse: () => User(id: 0, name: '-', email: '', role: null, createdAt: 0));
              final dept = allDepartments.firstWhere((d) => d.id == task.departmentId, orElse: () => Department(id: 0, name: '-', description: '', createdAt: 0));
              return pw.TableRow(
                children: [
                  pw.Padding(
                    padding: const pw.EdgeInsets.all(4),
                    child: pw.Text(task.title, style: pw.TextStyle(font: arabicFont)), // title is non-nullable
                  ),
                  pw.Padding(
                    padding: const pw.EdgeInsets.all(4),
                    child: pw.Text(task.status, style: pw.TextStyle(font: arabicFont)), // status is non-nullable
                  ),
                  pw.Padding(
                    padding: const pw.EdgeInsets.all(4),
                    child: pw.Text(dept.name, style: pw.TextStyle(font: arabicFont)),
                  ),
                  pw.Padding(
                    padding: const pw.EdgeInsets.all(4),
                    child: pw.Text(user.name, style: pw.TextStyle(font: arabicFont)),
                  ),
                  pw.Padding(
                    padding: const pw.EdgeInsets.all(4),
                    child: pw.Text(
                      task.startDate != null
                          ? DateFormat('yyyy-MM-dd').format(DateTime.fromMillisecondsSinceEpoch(task.startDate! * 1000))
                          : '-',
                      style: pw.TextStyle(font: arabicFont),
                    ),
                  ),
                  pw.Padding(
                    padding: const pw.EdgeInsets.all(4),
                    child: pw.Text(
                      task.dueDateDateTime != null
                          ? DateFormat('yyyy-MM-dd').format(task.dueDateDateTime!.toLocal())
                          : '-',
                      style: pw.TextStyle(font: arabicFont),
                    ),
                  ),
                ],
              );
            }),
          ],
        ),
      ],
      footer: (context) => pw.Container(
        alignment: pw.Alignment.center,
        margin: const pw.EdgeInsets.only(top: 8),
        child: pw.Text(
          'صفحة ${context.pageNumber} من ${context.pagesCount}',
          style: pw.TextStyle(font: arabicFontBold, fontSize: 12, color: PdfColors.grey800),
        ),
      ),
    ),
  );

  return pdf;
}
