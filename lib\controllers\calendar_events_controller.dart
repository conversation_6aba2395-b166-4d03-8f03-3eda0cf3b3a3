import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/calendar_models.dart';
import '../models/task_models.dart';
import '../services/api/calendar_events_api_service.dart';
import '../controllers/task_controller.dart';

/// متحكم التقويم الموحد - يدمج وظائف CalendarController و CalendarEventsController
class CalendarEventsController extends GetxController {
  final CalendarEventsApiService _apiService = CalendarEventsApiService();

  // قوائم الأحداث
  final RxList<CalendarEvent> _allEvents = <CalendarEvent>[].obs;
  final RxList<CalendarEvent> _filteredEvents = <CalendarEvent>[].obs;
  final RxList<CalendarEvent> _todayEvents = <CalendarEvent>[].obs;
  final RxList<CalendarEvent> _upcomingEvents = <CalendarEvent>[].obs;

  // الحدث الحالي
  final Rx<CalendarEvent?> _currentEvent = Rx<CalendarEvent?>(null);

  // حالة التحميل والأخطاء
  final RxBool _isLoading = false.obs;
  final RxString _error = ''.obs;

  // التاريخ المحدد (من CalendarController)
  final Rx<DateTime> _selectedDate = DateTime.now().obs;

  // المرشحات
  final RxString _searchQuery = ''.obs;
  final Rx<DateTime?> _dateFilter = Rx<DateTime?>(null);
  final Rx<int?> _userFilter = Rx<int?>(null);
  final RxBool _showActiveOnly = true.obs;

  // Getters
  List<CalendarEvent> get allEvents => _allEvents;
  List<CalendarEvent> get filteredEvents => _filteredEvents;
  List<CalendarEvent> get todayEvents => _todayEvents;
  List<CalendarEvent> get upcomingEvents => _upcomingEvents;
  CalendarEvent? get currentEvent => _currentEvent.value;
  bool get isLoading => _isLoading.value;
  String get error => _error.value;
  String get searchQuery => _searchQuery.value;
  DateTime? get dateFilter => _dateFilter.value;
  int? get userFilter => _userFilter.value;
  bool get showActiveOnly => _showActiveOnly.value;

  // Getters إضافية من CalendarController
  List<CalendarEvent> get events => _allEvents; // للتوافق مع CalendarController
  DateTime get selectedDate => _selectedDate.value;

  @override
  void onInit() {
    super.onInit();
    loadAllEvents();
    loadTodayEvents();
    loadUpcomingEvents();

    // الاستماع لتغييرات المهام
    _listenToTaskChanges();
  }

  /// الاستماع لتغييرات المهام لمزامنة التقويم
  void _listenToTaskChanges() {
    try {
      final taskController = Get.find<TaskController>();

      // مراقبة تغييرات قائمة المهام
      // استخدام worker بدلاً من ever لتجنب مشاكل النوع
      _allEvents.listen((events) {
        // يتم استدعاء هذا عند تغيير الأحداث
      });

      debugPrint('✅ تم إعداد مراقبة التغييرات - يمكن استدعاء syncTasksWithCalendar() يدوياً');
    } catch (e) {
      debugPrint('❌ خطأ في تفعيل مراقبة تغييرات المهام: $e');
    }
  }

  /// إعادة تحميل الأحداث مع المهام
  Future<void> refreshEventsWithTasks() async {
    debugPrint('🔄 إعادة تحميل الأحداث مع المهام...');
    await loadAllEvents();
    await loadTodayEvents();
    await loadUpcomingEvents();
  }

  /// تحميل جميع أحداث التقويم مع المهام
  Future<void> loadAllEvents() async {
    debugPrint('🚀 بدء تحميل جميع أحداث التقويم والمهام...');
    _isLoading.value = true;
    _error.value = '';

    try {
      debugPrint('📡 استدعاء API للحصول على الأحداث...');
      final events = await _apiService.getAllEvents();
      debugPrint('📥 تم استلام ${events.length} حدث من API');

      // تحميل المهام وتحويلها إلى أحداث تقويم
      await _loadTasksAsEvents();

      _allEvents.assignAll(events);
      _applyFilters();
      debugPrint('✅ تم تحميل ${events.length} حدث تقويم بنجاح');
    } catch (e) {
      _error.value = 'خطأ في تحميل أحداث التقويم: $e';
      debugPrint('❌ خطأ في تحميل أحداث التقويم: $e');
    } finally {
      _isLoading.value = false;
      debugPrint('🏁 انتهاء تحميل الأحداث - isLoading: ${_isLoading.value}');
    }
  }

  /// تحميل أحداث اليوم
  Future<void> loadTodayEvents() async {
    try {
      final events = await _apiService.getTodayEvents();
      _todayEvents.assignAll(events);
      debugPrint('تم تحميل ${events.length} حدث لليوم');
    } catch (e) {
      debugPrint('خطأ في تحميل أحداث اليوم: $e');
    }
  }

  /// تحميل الأحداث القادمة
  Future<void> loadUpcomingEvents() async {
    try {
      final events = await _apiService.getUpcomingEvents();
      _upcomingEvents.assignAll(events);
      debugPrint('تم تحميل ${events.length} حدث قادم');
    } catch (e) {
      debugPrint('خطأ في تحميل الأحداث القادمة: $e');
    }
  }

  /// الحصول على حدث تقويم بالمعرف
  Future<void> getEventById(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final event = await _apiService.getEventById(id);
      _currentEvent.value = event;
      debugPrint('تم تحميل حدث التقويم: ${event?.title ?? 'غير محدد'}');
    } catch (e) {
      _error.value = 'خطأ في تحميل حدث التقويم: $e';
      debugPrint('خطأ في تحميل حدث التقويم: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// إنشاء حدث تقويم جديد
  Future<bool> createEvent(CalendarEvent event) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      // التحقق من البيانات قبل الإرسال
      if (event.title.trim().isEmpty) {
        _error.value = 'عنوان الحدث مطلوب';
        return false;
      }

      if (event.userId <= 0) {
        _error.value = 'معرف المستخدم غير صحيح';
        return false;
      }

      if (event.startTime <= 0) {
        _error.value = 'وقت البداية مطلوب';
        return false;
      }

      if (event.endTime > 0 && event.startTime >= event.endTime) {
        _error.value = 'تاريخ البداية يجب أن يكون قبل تاريخ النهاية';
        return false;
      }

      debugPrint('🚀 محاولة إنشاء حدث: ${event.title}');
      debugPrint('📊 بيانات الحدث: userId=${event.userId}, taskId=${event.taskId}');

      final newEvent = await _apiService.createEvent(event);
      if (newEvent != null) {
        _allEvents.add(newEvent);
        _applyFilters();
        await loadTodayEvents();
        await loadUpcomingEvents();
        debugPrint('✅ تم إنشاء حدث تقويم جديد: ${newEvent.title}');
        return true;
      } else {
        _error.value = 'فشل في إنشاء الحدث - لم يتم إرجاع بيانات';
        debugPrint('❌ فشل في إنشاء الحدث - لم يتم إرجاع بيانات');
        return false;
      }
    } catch (e) {
      String errorMessage = 'خطأ في إنشاء حدث التقويم';

      if (e.toString().contains('400')) {
        errorMessage = 'البيانات المدخلة غير صحيحة';
      } else if (e.toString().contains('404')) {
        errorMessage = 'الخدمة غير متاحة';
      } else if (e.toString().contains('500')) {
        errorMessage = 'خطأ في الخادم';
      } else {
        errorMessage = 'خطأ في إنشاء حدث التقويم: $e';
      }

      _error.value = errorMessage;
      debugPrint('❌ $errorMessage');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحديث حدث تقويم
  Future<bool> updateEvent(int id, CalendarEvent event) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final updatedEvent = await _apiService.updateEvent(event);
      if (updatedEvent != null) {
        final index = _allEvents.indexWhere((e) => e.id == id);
        if (index != -1) {
          _allEvents[index] = updatedEvent;
          _applyFilters();
        }
      }
      await loadTodayEvents();
      await loadUpcomingEvents();
      debugPrint('تم تحديث حدث التقويم: ${event.title}');
      return true;
    } catch (e) {
      _error.value = 'خطأ في تحديث حدث التقويم: $e';
      debugPrint('خطأ في تحديث حدث التقويم: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// حذف حدث تقويم
  Future<bool> deleteEvent(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.deleteEvent(id);
      _allEvents.removeWhere((e) => e.id == id);
      _applyFilters();
      await loadTodayEvents();
      await loadUpcomingEvents();
      debugPrint('تم حذف حدث التقويم');
      return true;
    } catch (e) {
      _error.value = 'خطأ في حذف حدث التقويم: $e';
      debugPrint('خطأ في حذف حدث التقويم: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على الأحداث حسب التاريخ
  Future<void> getEventsByDate(DateTime date) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final endDate = DateTime(date.year, date.month, date.day, 23, 59, 59);
      final events = await _apiService.getEventsByDateRange(date, endDate);
      _allEvents.assignAll(events);
      _applyFilters();
      debugPrint('تم تحميل ${events.length} حدث للتاريخ ${date.toString()}');
    } catch (e) {
      _error.value = 'خطأ في تحميل أحداث التاريخ: $e';
      debugPrint('خطأ في تحميل أحداث التاريخ: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تطبيق المرشحات
  void _applyFilters() {
    var filtered = _allEvents.where((event) {
      // مرشح البحث
      if (_searchQuery.value.isNotEmpty) {
        final query = _searchQuery.value.toLowerCase();
        if (!event.title.toLowerCase().contains(query) &&
            !(event.description?.toLowerCase().contains(query) ?? false)) {
          return false;
        }
      }

      // مرشح التاريخ
      if (_dateFilter.value != null) {
        final eventDate = DateTime.fromMillisecondsSinceEpoch(event.startTime * 1000);
        final filterDate = _dateFilter.value!;
        if (eventDate.year != filterDate.year ||
            eventDate.month != filterDate.month ||
            eventDate.day != filterDate.day) {
          return false;
        }
      }

      // مرشح المستخدم
      if (_userFilter.value != null && event.userId != _userFilter.value) {
        return false;
      }

      // مرشح النشط فقط (تحقق من أن الحدث لم ينته بعد)
      if (_showActiveOnly.value) {
        final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
        if (event.endTime < now) {
          return false;
        }
      }

      return true;
    }).toList();

    _filteredEvents.assignAll(filtered);
  }

  /// تعيين استعلام البحث
  void setSearchQuery(String query) {
    _searchQuery.value = query;
    _applyFilters();
  }

  /// تعيين مرشح التاريخ
  void setDateFilter(DateTime? date) {
    _dateFilter.value = date;
    _applyFilters();
  }

  /// تعيين مرشح المستخدم
  void setUserFilter(int? userId) {
    _userFilter.value = userId;
    _applyFilters();
  }

  /// تعيين مرشح النشط فقط
  void setActiveFilter(bool showActiveOnly) {
    _showActiveOnly.value = showActiveOnly;
    _applyFilters();
  }

  /// مسح جميع المرشحات
  void clearFilters() {
    _searchQuery.value = '';
    _dateFilter.value = null;
    _userFilter.value = null;
    _showActiveOnly.value = true;
    _applyFilters();
  }

  /// مسح رسالة الخطأ
  void clearError() {
    _error.value = '';
  }

  /// تحديث البيانات
  @override
  Future<void> refresh() async {
    await Future.wait([
      loadAllEvents(),
      loadTodayEvents(),
      loadUpcomingEvents(),
    ]);
  }

  // ===== وظائف إضافية من CalendarController =====

  /// تحميل الأحداث (للتوافق مع CalendarController)
  Future<void> loadEvents() async {
    await loadAllEvents();
  }

  /// الحصول على أحداث يوم معين (من CalendarController)
  List<CalendarEvent> getEventsForDay(DateTime day) {
    return _allEvents.where((event) {
      final eventDate = event.startDateTime;
      return eventDate.year == day.year &&
             eventDate.month == day.month &&
             eventDate.day == day.day;
    }).toList();
  }

  /// تحديد التاريخ المحدد (من CalendarController)
  void setSelectedDate(DateTime date) {
    _selectedDate.value = date;
  }

  /// البحث في الأحداث (محسن)
  void searchEvents(String query) {
    _searchQuery.value = query;
    _applyFilters();
  }

  /// تصفية الأحداث حسب النوع
  void filterEventsByType(CalendarEventType? type) {
    if (type == null) {
      _filteredEvents.assignAll(_allEvents);
    } else {
      _filteredEvents.assignAll(
        _allEvents.where((event) => event.eventType == type).toList()
      );
    }
  }

  /// الحصول على أحداث اليوم (محسن)
  List<CalendarEvent> getTodayEvents() {
    return getEventsForDay(DateTime.now());
  }

  // ===== وظائف التذكيرات =====

  /// تعيين تذكير لحدث
  Future<bool> setEventReminder(int eventId, int reminderMinutes) async {
    try {
      _isLoading.value = true;
      _error.value = '';

      final success = await _apiService.setEventReminder(eventId, reminderMinutes);

      if (success) {
        // تحديث الحدث في القائمة المحلية
        final eventIndex = _allEvents.indexWhere((event) => event.id == eventId);
        if (eventIndex != -1) {
          _allEvents[eventIndex] = _allEvents[eventIndex].copyWith(
            reminderMinutes: reminderMinutes
          );
          _applyFilters();
        }

        Get.snackbar(
          'نجح',
          'تم تعيين التذكير بنجاح',
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
      } else {
        _error.value = 'فشل في تعيين التذكير';
        Get.snackbar(
          'خطأ',
          'فشل في تعيين التذكير',
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }

      return success;
    } catch (e) {
      _error.value = 'خطأ في تعيين التذكير: $e';
      Get.snackbar(
        'خطأ',
        'خطأ في تعيين التذكير',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// إلغاء تذكير حدث
  Future<bool> removeEventReminder(int eventId) async {
    try {
      _isLoading.value = true;
      _error.value = '';

      final success = await _apiService.removeEventReminder(eventId);

      if (success) {
        // تحديث الحدث في القائمة المحلية
        final eventIndex = _allEvents.indexWhere((event) => event.id == eventId);
        if (eventIndex != -1) {
          _allEvents[eventIndex] = _allEvents[eventIndex].copyWith(
            reminderMinutes: null
          );
          _applyFilters();
        }

        Get.snackbar(
          'نجح',
          'تم إلغاء التذكير بنجاح',
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
      } else {
        _error.value = 'فشل في إلغاء التذكير';
        Get.snackbar(
          'خطأ',
          'فشل في إلغاء التذكير',
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }

      return success;
    } catch (e) {
      _error.value = 'خطأ في إلغاء التذكير: $e';
      Get.snackbar(
        'خطأ',
        'خطأ في إلغاء التذكير',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على الأحداث التي لها تذكيرات
  List<CalendarEvent> getEventsWithReminders() {
    return _allEvents.where((event) =>
      event.reminderMinutes != null && event.reminderMinutes! > 0
    ).toList();
  }

  /// الحصول على الأحداث القادمة التي تحتاج تذكير
  List<CalendarEvent> getUpcomingEventsNeedingReminder() {
    final now = DateTime.now();
    return _allEvents.where((event) =>
      event.startDateTime.isAfter(now) &&
      event.reminderMinutes != null &&
      event.reminderMinutes! > 0
    ).toList();
  }

  // ===== إحصائيات التقويم المحسنة =====

  /// إحصائيات الأحداث الشاملة
  Map<String, dynamic> get eventStatistics {
    final stats = <String, dynamic>{
      'total': _allEvents.length,
      'today': 0,
      'thisWeek': 0,
      'thisMonth': 0,
      'thisYear': 0,
      'upcoming': 0,
      'overdue': 0,
      'withReminders': 0,
      'linkedToTasks': 0,
      'byType': <String, int>{},
      'byPriority': <String, int>{
        'high': 0,
        'medium': 0,
        'low': 0,
      },
      'byStatus': <String, int>{
        'active': 0,
        'completed': 0,
        'cancelled': 0,
      },
      'monthlyTrend': <String, int>{},
    };

    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final weekStart = today.subtract(Duration(days: today.weekday - 1));
    final monthStart = DateTime(now.year, now.month, 1);
    final yearStart = DateTime(now.year, 1, 1);

    for (final event in _allEvents) {
      final eventDate = event.startDateTime;
      final eventDay = DateTime(eventDate.year, eventDate.month, eventDate.day);
      final eventEndDate = event.endDateTime;

      // إحصائيات اليوم
      if (eventDay == today) {
        stats['today']++;
      }

      // إحصائيات الأسبوع
      if (eventDay.isAfter(weekStart.subtract(const Duration(days: 1))) &&
          eventDay.isBefore(weekStart.add(const Duration(days: 7)))) {
        stats['thisWeek']++;
      }

      // إحصائيات الشهر
      if (eventDay.isAfter(monthStart.subtract(const Duration(days: 1))) &&
          eventDate.month == now.month &&
          eventDate.year == now.year) {
        stats['thisMonth']++;
      }

      // إحصائيات السنة
      if (eventDay.isAfter(yearStart.subtract(const Duration(days: 1))) &&
          eventDate.year == now.year) {
        stats['thisYear']++;
      }

      // الأحداث القادمة (خلال الأسبوع القادم)
      if (eventDate.isAfter(now) &&
          eventDate.isBefore(now.add(const Duration(days: 7)))) {
        stats['upcoming']++;
      }

      // الأحداث المتأخرة (الأحداث التي انتهت ولم يتم حذفها)
      if (eventEndDate.isBefore(now) && !event.isDeleted) {
        stats['overdue']++;
      }

      // الأحداث مع التنبيهات
      if (event.isReminderEnabled) {
        stats['withReminders']++;
      }

      // الأحداث المرتبطة بالمهام
      if (event.taskId != null) {
        stats['linkedToTasks']++;
      }

      // إحصائيات حسب النوع
      final typeKey = event.eventType.toString().split('.').last;
      stats['byType'][typeKey] = (stats['byType'][typeKey] ?? 0) + 1;

      // إحصائيات حسب الأولوية (افتراضية بناءً على نوع الحدث)
      String priority = 'medium';
      switch (event.eventType) {
        case CalendarEventType.meeting:
          priority = 'high';
          break;
        case CalendarEventType.task:
          priority = 'medium';
          break;
        case CalendarEventType.reminder:
          priority = 'low';
          break;
        default:
          priority = 'medium';
      }
      stats['byPriority'][priority]++;

      // إحصائيات حسب الحالة
      if (event.isDeleted) {
        stats['byStatus']['cancelled']++;
      } else if (eventEndDate.isBefore(now)) {
        stats['byStatus']['completed']++;
      } else {
        stats['byStatus']['active']++;
      }

      // الاتجاه الشهري (آخر 6 أشهر)
      final monthKey = '${eventDate.year}-${eventDate.month.toString().padLeft(2, '0')}';
      stats['monthlyTrend'][monthKey] = (stats['monthlyTrend'][monthKey] ?? 0) + 1;
    }

    return stats;
  }

  /// الحصول على أحداث الشهر الحالي
  List<CalendarEvent> getCurrentMonthEvents() {
    final now = DateTime.now();
    return _allEvents.where((event) {
      final eventDate = event.startDateTime;
      return eventDate.year == now.year && eventDate.month == now.month;
    }).toList();
  }

  /// الحصول على أحداث الأسبوع الحالي
  List<CalendarEvent> getCurrentWeekEvents() {
    final now = DateTime.now();
    final weekStart = now.subtract(Duration(days: now.weekday - 1));
    final weekEnd = weekStart.add(const Duration(days: 6));

    return _allEvents.where((event) {
      final eventDate = event.startDateTime;
      return eventDate.isAfter(weekStart.subtract(const Duration(days: 1))) &&
             eventDate.isBefore(weekEnd.add(const Duration(days: 1)));
    }).toList();
  }

  /// الحصول على الأحداث المتأخرة
  List<CalendarEvent> getOverdueEvents() {
    final now = DateTime.now();
    return _allEvents.where((event) =>
      event.endDateTime.isBefore(now) && !event.isDeleted
    ).toList();
  }

  /// الحصول على أحداث حسب نوع معين
  List<CalendarEvent> getEventsByType(CalendarEventType type) {
    return _allEvents.where((event) => event.eventType == type).toList();
  }

  /// الحصول على أحداث المستخدم
  List<CalendarEvent> getUserEvents(int userId) {
    return _allEvents.where((event) => event.userId == userId).toList();
  }

  /// تصدير الأحداث (تحضير البيانات للتصدير)
  Map<String, dynamic> exportEventsData({
    DateTime? startDate,
    DateTime? endDate,
    CalendarEventType? eventType,
    int? userId,
  }) {
    var eventsToExport = _allEvents.where((event) {
      // تصفية حسب التاريخ
      if (startDate != null && event.startDateTime.isBefore(startDate)) {
        return false;
      }
      if (endDate != null && event.startDateTime.isAfter(endDate)) {
        return false;
      }

      // تصفية حسب النوع
      if (eventType != null && event.eventType != eventType) {
        return false;
      }

      // تصفية حسب المستخدم
      if (userId != null && event.userId != userId) {
        return false;
      }

      return true;
    }).toList();

    return {
      'events': eventsToExport.map((event) => event.toJson()).toList(),
      'summary': {
        'totalEvents': eventsToExport.length,
        'dateRange': {
          'start': startDate?.toIso8601String(),
          'end': endDate?.toIso8601String(),
        },
        'filters': {
          'eventType': eventType?.toString(),
          'userId': userId,
        },
        'exportDate': DateTime.now().toIso8601String(),
      },
    };
  }

  /// تحميل المهام وتحويلها إلى أحداث تقويم
  Future<void> _loadTasksAsEvents() async {
    try {
      debugPrint('🔄 بدء تحميل المهام لتحويلها إلى أحداث تقويم...');

      // الحصول على TaskController
      final taskController = Get.find<TaskController>();

      // تحميل المهام حسب صلاحيات المستخدم
      await taskController.loadAllTasks(forceRefresh: true);

      // فلترة المهام المناسبة للعرض في التقويم
      final filteredTasks = _filterTasksForCalendar(taskController.allTasks);

      // تحويل المهام إلى أحداث تقويم
      final taskEvents = <CalendarEvent>[];
      for (final task in filteredTasks) {
        final calendarEvent = _convertTaskToCalendarEvent(task);
        if (calendarEvent != null) {
          taskEvents.add(calendarEvent);
        }
      }

      // إضافة أحداث المهام إلى قائمة الأحداث
      _allEvents.addAll(taskEvents);

      debugPrint('✅ تم تحويل ${taskEvents.length} مهمة إلى أحداث تقويم');
    } catch (e) {
      debugPrint('❌ خطأ في تحميل المهام كأحداث تقويم: $e');
    }
  }

  /// تحويل مهمة إلى حدث تقويم
  CalendarEvent? _convertTaskToCalendarEvent(Task task) {
    try {
      // تحديد تاريخ البداية والنهاية
      int startTime;
      int endTime;

      if (task.startDate != null && task.dueDate != null) {
        startTime = task.startDate!;
        endTime = task.dueDate!;
      } else if (task.dueDate != null) {
        // إذا كان هناك موعد نهائي فقط، نجعل الحدث في نفس اليوم
        endTime = task.dueDate!;
        startTime = endTime - (8 * 3600); // 8 ساعات قبل الموعد النهائي
      } else if (task.startDate != null) {
        // إذا كان هناك تاريخ بداية فقط، نجعل الحدث لمدة يوم واحد
        startTime = task.startDate!;
        endTime = startTime + (24 * 3600); // يوم واحد
      } else {
        // إذا لم تكن هناك تواريخ، لا نعرض المهمة في التقويم
        return null;
      }

      // تحديد لون المهمة حسب الأولوية والحالة
      String color = _getTaskColor(task);

      // إنشاء حدث التقويم
      return CalendarEvent(
        id: -task.id, // استخدام ID سالب للتمييز عن الأحداث العادية
        title: task.title,
        description: task.description ?? 'مهمة: ${task.title}',
        startTime: startTime,
        endTime: endTime,
        allDay: _isTaskAllDay(task),
        location: null,
        color: color,
        userId: task.assigneeId ?? task.creatorId,
        taskId: task.id,
        recurrenceRule: null,
        createdAt: task.createdAt,
        updatedAt: null,
        isDeleted: task.isDeleted,
        duration: endTime - startTime,
        reminderMinutes: _getTaskReminderMinutes(task),
        eventType: CalendarEventType.task,
        recurrencePattern: EventRecurrencePattern.none,
        recurrenceCount: 0,
        recurrenceEndDate: null,
        reminderTime: EventReminderTime.none,
        isReminderEnabled: false,
        user: null,
        task: task,
      );
    } catch (e) {
      debugPrint('❌ خطأ في تحويل المهمة ${task.id} إلى حدث تقويم: $e');
      return null;
    }
  }

  /// تحديد لون المهمة حسب الأولوية والحالة
  String _getTaskColor(Task task) {
    // ألوان حسب الحالة
    switch (task.status.toLowerCase()) {
      case 'completed':
        return '#4CAF50'; // أخضر للمكتملة
      case 'in_progress':
        return '#2196F3'; // أزرق للجارية
      case 'pending':
        return '#FF9800'; // برتقالي للمعلقة
      case 'cancelled':
        return '#9E9E9E'; // رمادي للملغاة
      default:
        // ألوان حسب الأولوية
        switch (task.priority.toLowerCase()) {
          case 'high':
            return '#F44336'; // أحمر للعالية
          case 'medium':
            return '#FF9800'; // برتقالي للمتوسطة
          case 'low':
            return '#4CAF50'; // أخضر للمنخفضة
          default:
            return '#2196F3'; // أزرق افتراضي
        }
    }
  }

  /// تحديد ما إذا كانت المهمة تستغرق اليوم كاملاً
  bool _isTaskAllDay(Task task) {
    if (task.startDate == null || task.dueDate == null) {
      return true; // إذا لم تكن هناك أوقات محددة، اعتبرها يوم كامل
    }

    final duration = task.dueDate! - task.startDate!;
    return duration >= (24 * 3600); // إذا كانت المدة 24 ساعة أو أكثر
  }

  /// تحديد دقائق التذكير للمهمة
  int? _getTaskReminderMinutes(Task task) {
    if (task.dueDate == null) return null;

    // تذكير قبل يوم واحد للمهام العالية الأولوية
    if (task.priority.toLowerCase() == 'high') {
      return 24 * 60; // 24 ساعة
    }

    // تذكير قبل ساعتين للمهام المتوسطة
    if (task.priority.toLowerCase() == 'medium') {
      return 2 * 60; // ساعتان
    }

    // تذكير قبل ساعة للمهام المنخفضة
    return 60; // ساعة واحدة
  }

  /// فلترة المهام لعرضها في التقويم
  List<Task> _filterTasksForCalendar(List<Task> tasks) {
    return tasks.where((task) {
      // عرض المهام التي لها تواريخ فقط
      if (task.startDate == null && task.dueDate == null) {
        return false;
      }

      // عدم عرض المهام المحذوفة
      if (task.isDeleted) {
        return false;
      }

      // عدم عرض المهام المكتملة إذا كان المستخدم لا يريد رؤيتها
      if (task.status.toLowerCase() == 'completed' && _showActiveOnly.value) {
        return false;
      }

      return true;
    }).toList();
  }

  /// تحديث أحداث المهام عند تغيير المهام
  Future<void> syncTasksWithCalendar() async {
    try {
      debugPrint('🔄 مزامنة المهام مع التقويم...');

      // إزالة أحداث المهام السابقة (التي لها ID سالب)
      _allEvents.removeWhere((event) => event.id < 0);

      // إعادة تحميل المهام كأحداث
      await _loadTasksAsEvents();

      // تطبيق المرشحات
      _applyFilters();

      debugPrint('✅ تم مزامنة المهام مع التقويم بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في مزامنة المهام مع التقويم: $e');
    }
  }
}
