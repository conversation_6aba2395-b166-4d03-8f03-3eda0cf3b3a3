import 'package:flutter/material.dart';
import 'package:flutter_application_2/services/api/task_api_service.dart';
import 'package:get/get.dart';

import '../models/report_models.dart';
import '../models/report_criteria_model.dart';
import '../models/reporting/report_result_model.dart';
import '../models/task_models.dart';
import '../models/user_model.dart';
import 'api/reports_api_service.dart';
import 'api/user_api_service.dart';

/// خدمة التقارير الموحدة
/// 
/// تجمع جميع وظائف التقارير في مكان واحد وتوفر واجهة موحدة
/// لإنشاء وإدارة وتصدير التقارير المختلفة
class UnifiedReportService extends GetxService {
  final ReportsApiService _reportsApiService = ReportsApiService();
  final TaskApiService  _tasksApiService = TaskApiService ();
  final UserApiService _userApiService = UserApiService();

  // ===== إدارة التقارير الأساسية =====

  /// الحصول على جميع التقارير
  Future<List<Report>> getAllReports() async {
    try {
      return await _reportsApiService.getAllReports();
    } catch (e) {
      debugPrint('خطأ في الحصول على التقارير: $e');
      rethrow;
    }
  }

  /// الحصول على تقرير محدد
  Future<Report?> getReport(int reportId) async {
    try {
      return await _reportsApiService.getReport(reportId);
    } catch (e) {
      debugPrint('خطأ في الحصول على التقرير $reportId: $e');
      return null;
    }
  }

  /// إنشاء تقرير جديد
  Future<Report> createReport({
    required String title,
    String? description,
    required ReportType type,
    required int createdById,
    required ReportCriteria criteria,
  }) async {
    try {
      final report = Report(
        id: 0, // سيتم تعيينه من الخادم
        title: title,
        description: description,
        reportType: type,
        query: _generateQueryFromCriteria(type, criteria),
        parameters: _serializeCriteria(criteria),
        createdBy: createdById,
        createdAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
        isPublic: false,
        isDeleted: false,
      );
      return await _reportsApiService.createReport(report);
    } catch (e) {
      debugPrint('خطأ في إنشاء التقرير: $e');
      rethrow;
    }
  }

  /// تنفيذ تقرير
  Future<ReportResult> executeReport(int reportId) async {
    try {
      final result = await _reportsApiService.runReport(reportId, {});
      return ReportResult.fromJson(result);
    } catch (e) {
      debugPrint('خطأ في تنفيذ التقرير: $e');
      return ReportResult.failure(
        generatedAt: DateTime.now(),
        errorMessages: [e.toString()],
      );
    }
  }

  /// حذف تقرير
  Future<bool> deleteReport(int reportId) async {
    try {
      await _reportsApiService.deleteReport(reportId);
      return true;
    } catch (e) {
      debugPrint('خطأ في حذف التقرير $reportId: $e');
      return false;
    }
  }

  // ===== تقارير المهام =====

  /// إنشاء تقرير ملخص المهام
  Future<ReportResult> generateTaskSummaryReport({
    DateTime? startDate,
    DateTime? endDate,
    List<int>? departmentIds,
    List<int>? userIds,
    List<String>? statuses,
  }) async {
    try {
      final tasks = await _getFilteredTasks(
        startDate: startDate,
        endDate: endDate,
        departmentIds: departmentIds,
        userIds: userIds,
        statuses: statuses,
      );

      final statistics = _calculateTaskStatistics(tasks);
      
      return ReportResult.success(
        generatedAt: DateTime.now(),
        summary: statistics,
        data: tasks.map((task) => task.toJson()).toList(),
        totalRecords: tasks.length,
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء تقرير ملخص المهام: $e');
      return ReportResult.failure(
        generatedAt: DateTime.now(),
        errorMessages: [e.toString()],
      );
    }
  }

  /// إنشاء تقرير أداء المستخدمين
  Future<ReportResult> generateUserPerformanceReport({
    DateTime? startDate,
    DateTime? endDate,
    List<int>? userIds,
  }) async {
    try {
      final users = userIds != null 
          ? await _getUsersByIds(userIds)
          : await _userApiService.getAllUsers();

      final tasks = await _getFilteredTasks(
        startDate: startDate,
        endDate: endDate,
        userIds: userIds,
      );

      final performanceData = _calculateUserPerformance(users, tasks);
      
      return ReportResult.success(
        generatedAt: DateTime.now(),
        summary: {
          'total_users': users.length,
          'total_tasks': tasks.length,
          'average_completion_rate': _calculateAverageCompletionRate(performanceData),
        },
        data: performanceData,
        totalRecords: users.length,
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء تقرير أداء المستخدمين: $e');
      return ReportResult.failure(
        generatedAt: DateTime.now(),
        errorMessages: [e.toString()],
      );
    }
  }

  /// إنشاء تقرير أداء الأقسام
  Future<ReportResult> generateDepartmentPerformanceReport({
    DateTime? startDate,
    DateTime? endDate,
    List<int>? departmentIds,
  }) async {
    try {
      // سيتم تطوير هذا لاحقاً عند إضافة API الأقسام
      return ReportResult.failure(
        generatedAt: DateTime.now(),
        errorMessages: ['تقرير أداء الأقسام قيد التطوير'],
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء تقرير أداء الأقسام: $e');
      return ReportResult.failure(
        generatedAt: DateTime.now(),
        errorMessages: [e.toString()],
      );
    }
  }

  // ===== تصدير التقارير =====

  /// تصدير تقرير إلى PDF
  Future<String?> exportReportToPdf(int reportId) async {
    try {
      final result = await _reportsApiService.exportReport(reportId, 'pdf', null);
      return result['filePath'] as String?;
    } catch (e) {
      debugPrint('خطأ في تصدير التقرير إلى PDF: $e');
      return null;
    }
  }

  /// تصدير تقرير إلى Excel
  Future<String?> exportReportToExcel(int reportId) async {
    try {
      final result = await _reportsApiService.exportReport(reportId, 'excel', null);
      return result['filePath'] as String?;
    } catch (e) {
      debugPrint('خطأ في تصدير التقرير إلى Excel: $e');
      return null;
    }
  }

  /// تصدير تقرير إلى CSV
  Future<String?> exportReportToCsv(int reportId) async {
    try {
      final result = await _reportsApiService.exportReport(reportId, 'csv', null);
      return result['filePath'] as String?;
    } catch (e) {
      debugPrint('خطأ في تصدير التقرير إلى CSV: $e');
      return null;
    }
  }

  // ===== الدوال المساعدة الخاصة =====

  /// جلب المهام مع التصفية
  Future<List<Task>> _getFilteredTasks({
    DateTime? startDate,
    DateTime? endDate,
    List<int>? departmentIds,
    List<int>? userIds,
    List<String>? statuses,
  }) async {
    final allTasks = await _tasksApiService.getAllTasks(forceRefresh: true);
    
    return allTasks.where((task) {
      // تصفية بالتاريخ
      if (startDate != null && task.createdAtDateTime.isBefore(startDate)) {
        return false;
      }
      if (endDate != null && task.createdAtDateTime.isAfter(endDate)) {
        return false;
      }
      
      // تصفية بالأقسام
      if (departmentIds != null && departmentIds.isNotEmpty) {
        if (task.departmentId == null || !departmentIds.contains(task.departmentId)) {
          return false;
        }
      }
      
      // تصفية بالمستخدمين
      if (userIds != null && userIds.isNotEmpty) {
        if (!userIds.contains(task.assigneeId) && !userIds.contains(task.creatorId)) {
          return false;
        }
      }
      
      // تصفية بالحالة
      if (statuses != null && statuses.isNotEmpty) {
        if (!statuses.contains(task.status)) {
          return false;
        }
      }
      
      return true;
    }).toList();
  }

  /// جلب المستخدمين بالمعرفات
  Future<List<User>> _getUsersByIds(List<int> userIds) async {
    final allUsers = await _userApiService.getAllUsers();
    return allUsers.where((user) => userIds.contains(user.id)).toList();
  }

  /// حساب إحصائيات المهام
  Map<String, dynamic> _calculateTaskStatistics(List<Task> tasks) {
    final totalTasks = tasks.length;
    final completedTasks = tasks.where((t) => t.status == 'مكتملة').length;
    final inProgressTasks = tasks.where((t) => t.status == 'قيد التنفيذ').length;
    final pendingTasks = tasks.where((t) => t.status == 'معلقة').length;
    
    return {
      'total_tasks': totalTasks,
      'completed_tasks': completedTasks,
      'in_progress_tasks': inProgressTasks,
      'pending_tasks': pendingTasks,
      'completion_rate': totalTasks > 0 ? (completedTasks / totalTasks * 100) : 0.0,
    };
  }

  /// حساب أداء المستخدمين
  List<Map<String, dynamic>> _calculateUserPerformance(List<User> users, List<Task> tasks) {
    return users.map((user) {
      final userTasks = tasks.where((t) => t.assigneeId == user.id).toList();
      final completedTasks = userTasks.where((t) => t.status == 'مكتملة').length;
      
      return {
        'user_id': user.id,
        'user_name': user.name,
        'total_tasks': userTasks.length,
        'completed_tasks': completedTasks,
        'completion_rate': userTasks.isNotEmpty ? (completedTasks / userTasks.length * 100) : 0.0,
        'average_completion_percentage': userTasks.isNotEmpty 
            ? userTasks.map((t) => t.completionPercentage).reduce((a, b) => a + b) / userTasks.length
            : 0.0,
      };
    }).toList();
  }

  /// حساب متوسط معدل الإنجاز
  double _calculateAverageCompletionRate(List<Map<String, dynamic>> performanceData) {
    if (performanceData.isEmpty) return 0.0;
    
    final totalRate = performanceData
        .map((data) => data['completion_rate'] as double)
        .reduce((a, b) => a + b);
    
    return totalRate / performanceData.length;
  }

  /// توليد استعلام من المعايير
  String _generateQueryFromCriteria(ReportType type, ReportCriteria criteria) {
    // توليد استعلامات حقيقية حسب نوع التقرير
    switch (type) {
      case ReportType.taskSummary:
        return 'SELECT * FROM Tasks WHERE is_deleted = 0';
      case ReportType.userPerformance:
        return 'SELECT * FROM users WHERE is_active = 1';
      case ReportType.departmentPerformance:
        return 'SELECT * FROM departments WHERE is_active = 1';
      default:
        return 'SELECT COUNT(*) as total FROM Tasks WHERE is_deleted = 0';
    }
  }

  /// تسلسل المعايير إلى JSON
  String _serializeCriteria(ReportCriteria criteria) {
    return criteria.toJson().toString();
  }
}
