using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Logging;
using System.Collections;
using webApi.Models;
using webApi.Hubs;
using webApi.Services;
using Microsoft.AspNetCore.SignalR;

namespace webApi.Controllers
{
    /// <summary>
    /// وحدة تحكم لإدارة المهام في نظام إدارة المهام
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [Produces("application/json")]
    public class TasksController : ControllerBase
    {
        private readonly TasksDbContext _context;
        private readonly ILogger<TasksController> _logger;
        private readonly IHubContext<TaskHub> _hubContext;
        private readonly INotificationService _notificationService;
        private readonly ILoggingService _loggingService;

        public TasksController(
            TasksDbContext context,
            ILogger<TasksController> logger,
            IHubContext<TaskHub> hubContext,
            INotificationService notificationService,
            ILoggingService loggingService)
        {
            _context = context;
            _logger = logger;
            _hubContext = hubContext;
            _notificationService = notificationService;
            _loggingService = loggingService;
        }

        /// <summary>
        /// الحصول على جميع المهام مع البيانات المرتبطة ودعم Pagination والبحث
        /// </summary>
        /// <param name="page">رقم الصفحة (افتراضي: 1)</param>
        /// <param name="pageSize">حجم الصفحة (افتراضي: 50)</param>
        /// <param name="search">نص البحث</param>
        /// <param name="searchQuery">نص البحث (بديل)</param>
        /// <param name="orderBy">حقل الترتيب</param>
        /// <param name="sortBy">حقل الترتيب (بديل)</param>
        /// <param name="orderDirection">اتجاه الترتيب (ASC/DESC)</param>
        /// <param name="sortOrder">اتجاه الترتيب (بديل)</param>
        /// <param name="status">فلتر حسب الحالة</param>
        /// <param name="priority">فلتر حسب الأولوية</param>
        /// <param name="assigneeId">فلتر حسب المكلف</param>
        /// <param name="departmentId">فلتر حسب القسم</param>
        /// <param name="created_at_start">بداية نطاق تاريخ الإنشاء (Unix timestamp)</param>
        /// <param name="created_at_end">نهاية نطاق تاريخ الإنشاء (Unix timestamp)</param>
        /// <param name="start_date_start">بداية نطاق تاريخ البدء (Unix timestamp)</param>
        /// <param name="start_date_end">نهاية نطاق تاريخ البدء (Unix timestamp)</param>
        /// <param name="due_date_start">بداية نطاق تاريخ الاستحقاق (Unix timestamp)</param>
        /// <param name="due_date_end">نهاية نطاق تاريخ الاستحقاق (Unix timestamp)</param>
        /// <param name="completed_at_start">بداية نطاق تاريخ الإكمال (Unix timestamp)</param>
        /// <param name="completed_at_end">نهاية نطاق تاريخ الإكمال (Unix timestamp)</param>
        /// <returns>قائمة مقسمة من المهام</returns>
        /// <response code="200">إرجاع قائمة المهام مع معلومات Pagination</response>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<object>> GetTasks(
            int page = 1,
            int pageSize = 50,
            string? search = null,
            string? searchQuery = null,
            string? orderBy = null,
            string? sortBy = null,
            string orderDirection = "ASC",
            string? sortOrder = null,
            string? status = null,
            string? priority = null,
            int? assigneeId = null,
            int? departmentId = null,
            long? created_at_start = null,
            long? created_at_end = null,
            long? start_date_start = null,
            long? start_date_end = null,
            long? due_date_start = null,
            long? due_date_end = null,
            long? completed_at_start = null,
            long? completed_at_end = null)
        {
            try
            {
                _logger.LogInformation("طلب الحصول على المهام - الصفحة: {Page}, الحجم: {PageSize}", page, pageSize);

                // تسجيل فلاتر التاريخ المطبقة
                if (created_at_start.HasValue || created_at_end.HasValue)
                    _logger.LogInformation("فلتر تاريخ الإنشاء: من {Start} إلى {End}", created_at_start, created_at_end);

                if (start_date_start.HasValue || start_date_end.HasValue)
                    _logger.LogInformation("فلتر تاريخ البدء: من {Start} إلى {End}", start_date_start, start_date_end);

                if (due_date_start.HasValue || due_date_end.HasValue)
                    _logger.LogInformation("فلتر تاريخ الاستحقاق: من {Start} إلى {End}", due_date_start, due_date_end);

                // توحيد معاملات البحث والترتيب
                var searchTerm = search ?? searchQuery;
                var sortField = orderBy ?? sortBy ?? "Id";
                var sortDir = orderDirection ?? sortOrder ?? "ASC";

                // تسجيل عملية البحث والاستعلام
                var currentUserId = GetCurrentUserId();
                if (currentUserId > 0)
                {
                    var searchDetails = new Dictionary<string, object>
                    {
                        ["page"] = page,
                        ["page_size"] = pageSize,
                        ["search_term"] = searchTerm ?? "",
                        ["status_filter"] = status ?? "",
                        ["priority_filter"] = priority ?? "",
                        ["assignee_filter"] = assigneeId ?? 0,
                        ["department_filter"] = departmentId ?? 0,
                        ["sort_field"] = sortField,
                        ["sort_direction"] = sortDir
                    };

                    await _loggingService.LogActivityAsync(
                        "search",
                        "general",
                        currentUserId,
                        currentUserId,
                        $"بحث في المهام مع الفلاتر المطبقة",
                        newValue: System.Text.Json.JsonSerializer.Serialize(searchDetails));
                }

                // التحقق من صحة المعاملات
                if (page < 1) page = 1;
                if (pageSize < 1 || pageSize > 100) pageSize = 50;

                // بناء الاستعلام الأساسي مع تحسين الأداء
                var query = _context.Tasks
                    .Where(t => !t.IsDeleted)
                    .AsQueryable();

                // تطبيق الفلاتر أولاً لتقليل البيانات
                if (status != null)
                    query = query.Where(t => t.Status == status);

                if (priority != null)
                    query = query.Where(t => t.Priority == priority);

                if (assigneeId.HasValue)
                    query = query.Where(t => t.AssigneeId == assigneeId.Value);

                if (departmentId.HasValue)
                    query = query.Where(t => t.DepartmentId == departmentId.Value);

                // تطبيق فلاتر التاريخ
                if (created_at_start.HasValue)
                    query = query.Where(t => t.CreatedAt >= created_at_start.Value);

                if (created_at_end.HasValue)
                    query = query.Where(t => t.CreatedAt <= created_at_end.Value);

                if (start_date_start.HasValue)
                    query = query.Where(t => t.StartDate.HasValue && t.StartDate.Value >= start_date_start.Value);

                if (start_date_end.HasValue)
                    query = query.Where(t => t.StartDate.HasValue && t.StartDate.Value <= start_date_end.Value);

                if (due_date_start.HasValue)
                    query = query.Where(t => t.DueDate.HasValue && t.DueDate.Value >= due_date_start.Value);

                if (due_date_end.HasValue)
                    query = query.Where(t => t.DueDate.HasValue && t.DueDate.Value <= due_date_end.Value);

                if (completed_at_start.HasValue)
                    query = query.Where(t => t.CompletedAt.HasValue && t.CompletedAt.Value >= completed_at_start.Value);

                if (completed_at_end.HasValue)
                    query = query.Where(t => t.CompletedAt.HasValue && t.CompletedAt.Value <= completed_at_end.Value);

                // تطبيق البحث
                if (!string.IsNullOrEmpty(searchTerm))
                {
                    query = query.Where(t =>
                        t.Title.Contains(searchTerm) ||
                        (t.Description != null && t.Description.Contains(searchTerm)));
                }

                // حساب إجمالي السجلات قبل تطبيق Include
                var totalRecords = await query.CountAsync();
                var totalPages = (int)Math.Ceiling((double)totalRecords / pageSize);

                _logger.LogInformation("تم العثور على {TotalRecords} مهمة", totalRecords);

                // تطبيق الترتيب
                query = ApplyTaskSorting(query, sortField, sortDir);

                // تطبيق Pagination ثم Include للحصول على البيانات المطلوبة فقط
                var tasks = await query
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .Include(t => t.Creator)
                    .Include(t => t.Assignee)
                    .Include(t => t.Department)
                    .Include(t => t.TaskType)
                    .ToListAsync();

                _logger.LogInformation("تم تحميل {TasksCount} مهمة للصفحة {Page}", tasks.Count, page);

                // إرجاع النتيجة بتنسيق موحد
                var result = new
                {
                    data = tasks,
                    totalRecords = totalRecords,
                    totalPages = totalPages,
                    currentPage = page,
                    pageSize = pageSize,
                    hasNextPage = page < totalPages,
                    hasPreviousPage = page > 1
                };

                _logger.LogInformation("تم إرجاع {TasksCount} مهمة بنجاح", tasks.Count);
                Console.WriteLine("////////////////////////////////// ممتاز  تم بنجاح/////////////////////////////////////////");
                return Ok(result);
            }
            catch (SqlException sqlEx)
            {
                _logger.LogError(sqlEx, "خطأ في قاعدة البيانات أثناء جلب المهام");
                return StatusCode(500, new
                {
                    success = false,
                    message = "خطأ في قاعدة البيانات",
                    error = "تعذر الاتصال بقاعدة البيانات. يرجى المحاولة لاحقاً."
                });
            }
            catch (InvalidOperationException invEx)
            {
                _logger.LogError(invEx, "خطأ في العملية أثناء جلب المهام");
                return StatusCode(500, new
                {
                    success = false,
                    message = "خطأ في تكوين النظام",        
                    error = "تعذر تنفيذ العملية. يرجى التحقق من إعدادات النظام."
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ غير متوقع أثناء جلب المهام");
                return StatusCode(500, new
                {
                    success = false,
                    message = "حدث خطأ غير متوقع أثناء جلب المهام",
                    error = ex.Message,
                    details = ex.InnerException?.Message
                });
            }
        }

        /// <summary>
        /// الحصول على مهمة محددة بواسطة المعرف
        /// </summary>
        /// <param name="id">معرف المهمة</param>
        /// <returns>تفاصيل المهمة</returns>
        /// <response code="200">إرجاع المهمة</response>
        /// <response code="404">إذا لم يتم العثور على المهمة</response>
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<Models.Task>> GetTask(int id)
        {
            try
            {
                _logger.LogInformation("طلب الحصول على المهمة: {TaskId}", id);

                // تحميل المهمة من قاعدة البيانات
                var task = await _context.Tasks
                    .AsNoTracking() // إضافة هذه السطر لمنع الكاش
                    .Include(t => t.Creator)
                    .Include(t => t.Assignee)
                    .Include(t => t.Department)
                    .Include(t => t.TaskType)
                    .Include(t => t.Subtasks)
                    .Include(t => t.TaskComments)
                    .Include(t => t.Attachments)
                    .FirstOrDefaultAsync(t => t.Id == id && !t.IsDeleted);

                if (task == null)
                {
                    _logger.LogWarning("لم يتم العثور على المهمة: {TaskId}", id);
                    return NotFound();
                }

                _logger.LogInformation("تم إرجاع المهمة: {TaskId} - {TaskTitle}", id, task.Title);

                // تسجيل عملية عرض المهمة
                var currentUserId = GetCurrentUserId();
                if (currentUserId > 0)
                {
                    await _loggingService.LogActivityAsync(
                        "view",
                        "task",
                        id,
                        currentUserId,
                        $"عرض تفاصيل المهمة: {task.Title}",
                        taskId: id);
                }

                return task;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على المهمة: {TaskId}", id);
                return StatusCode(500, new
                {
                    success = false,
                    message = "حدث خطأ أثناء جلب المهمة",
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// الحصول على بيانات شاملة للمهمة لإنشاء التقرير المفصل
        /// </summary>
        /// <param name="id">معرف المهمة</param>
        /// <returns>بيانات شاملة للمهمة مع جميع العلاقات والتفاصيل</returns>
        /// <response code="200">إرجاع البيانات الشاملة للمهمة</response>
        /// <response code="404">المهمة غير موجودة</response>
        [HttpGet("{id}/comprehensive-report-data")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<object>> GetTaskComprehensiveReportData(int id)
        {
            try
            {
                _logger.LogInformation("طلب الحصول على البيانات الشاملة للتقرير للمهمة: {TaskId}", id);

                // تحميل المهمة مع جميع البيانات المرتبطة بشكل شامل
                var task = await _context.Tasks
                    .Include(t => t.Creator)
                    .Include(t => t.Assignee)
                    .Include(t => t.Department)
                    .Include(t => t.TaskType)
                    .Include(t => t.Subtasks)
                    .Include(t => t.TaskComments)
                        .ThenInclude(tc => tc.User)
                    .Include(t => t.Attachments)
                        .ThenInclude(a => a.UploadedByNavigation)
                    .Include(t => t.TaskHistories)
                        .ThenInclude(th => th.ChangedByNavigation)
                    .Include(t => t.TaskHistories)
                        .ThenInclude(th => th.User)
                    .Include(t => t.Users) // المستخدمون الذين لديهم صلاحية الوصول
                    .FirstOrDefaultAsync(t => t.Id == id && !t.IsDeleted);

                if (task == null)
                {
                    _logger.LogWarning("لم يتم العثور على المهمة: {TaskId}", id);
                    return NotFound(new { message = "المهمة غير موجودة" });
                }

                // جلب بيانات المستخدمين المرتبطين بالمهمة من جدول TaskAccessUser
                var accessUsers = await _context.Set<TaskAccessUser>()
                    .Where(tau => tau.TaskId == id)
                    .Include(tau => tau.User)
                    .Select(tau => new
                    {
                        id = tau.User!.Id,
                        name = tau.User.Name,
                        email = tau.User.Email,
                        role = "مساهم",
                        accessGrantedAt = DateTime.Now // يمكن إضافة هذا الحقل لاحقاً
                    })
                    .ToListAsync();

                // جلب بيانات إضافية من جداول أخرى
                var taskProgressTrackers = await _context.TaskProgressTrackers
                    .Where(tpt => tpt.TaskId == id)
                    .Include(tpt => tpt.UpdatedByNavigation)
                    .Select(tpt => new
                    {
                        id = tpt.Id,
                        taskId = tpt.TaskId,
                        userId = tpt.UpdatedBy,
                        progressPercentage = tpt.ProgressPercentage,
                        notes = tpt.Notes,
                        updatedAt = tpt.UpdatedAt,
                        user = tpt.UpdatedByNavigation != null ? new
                        {
                            id = tpt.UpdatedByNavigation.Id,
                            name = tpt.UpdatedByNavigation.Name,
                            email = tpt.UpdatedByNavigation.Email
                        } : new
                        {
                            id = 0,
                            name = "مستخدم محذوف",
                            email = "غير متوفر"
                        }
                    })
                    .ToListAsync();

                var timeTrackingEntries = await _context.TimeTrackingEntries
                    .Where(tte => tte.TaskId == id)
                    .Include(tte => tte.User)
                    .Select(tte => new
                    {
                        id = tte.Id,
                        taskId = tte.TaskId,
                        userId = tte.UserId,
                        startTime = tte.StartTime,
                        endTime = tte.EndTime,
                        duration = tte.Duration,
                        description = tte.Description,
                        user = new
                        {
                            id = tte.User!.Id,
                            name = tte.User.Name,
                            email = tte.User.Email
                        }
                    })
                    .ToListAsync();

                // تحميل جميع الأدوار مرة واحدة في بداية الدالة
                var rolesDict = await _context.Roles.ToDictionaryAsync(r => r.Id, r => r.Name);

                // إنشاء كائن البيانات الشاملة المحسن
                var comprehensiveReportData = new
                {
                    // بيانات المهمة الأساسية
                    id = task.Id,
                    title = task.Title,
                    description = task.Description,
                    status = task.Status,
                    priority = task.Priority,
                    completionPercentage = task.CompletionPercentage,
                    createdAt = task.CreatedAt,
                    startDate = task.StartDate,
                    dueDate = task.DueDate,
                    completedAt = task.CompletedAt,
                    estimatedTime = task.EstimatedTime,
                    actualTime = task.ActualTime,
                    incoming = task.Incoming,
                    note = task.Note,
                    isDeleted = task.IsDeleted,

                    // المستخدمون المرتبطون
                    creator = task.Creator != null ? new
                    {
                        id = task.Creator.Id,
                        name = task.Creator.Name,
                        email = task.Creator.Email,
                        role = task.Creator.RoleId.HasValue && rolesDict.ContainsKey(task.Creator.RoleId.Value) ? rolesDict[task.Creator.RoleId.Value] : null,
                        departmentId = task.Creator.DepartmentId
                    } : null,

                    assignee = task.Assignee != null ? new
                    {
                        id = task.Assignee.Id,
                        name = task.Assignee.Name,
                        email = task.Assignee.Email,
                        role = task.Assignee.RoleId.HasValue && rolesDict.ContainsKey(task.Assignee.RoleId.Value) ? rolesDict[task.Assignee.RoleId.Value] : null,
                        departmentId = task.Assignee.DepartmentId
                    } : null,

                    department = task.Department != null ? new
                    {
                        id = task.Department.Id,
                        name = task.Department.Name,
                        description = task.Department.Description
                    } : null,

                    taskType = task.TaskType != null ? new
                    {
                        id = task.TaskType.Id,
                        name = task.TaskType.Name,
                        description = task.TaskType.Description
                    } : null,

                    // المستخدمون الذين لديهم صلاحية الوصول
                    accessUsers = accessUsers,

                    // التعليقات مع بيانات المستخدمين المفصلة
                    comments = task.TaskComments.Select(tc => new
                    {
                        id = tc.Id,
                        content = tc.Content,
                        createdAt = tc.CreatedAt,
                        isEdited = false, // يمكن إضافة هذا الحقل لاحقاً
                        user = tc.User != null ? new
                        {
                            id = tc.User.Id,
                            name = tc.User.Name,
                            email = tc.User.Email,
                            role = tc.User.RoleId.HasValue && rolesDict.ContainsKey(tc.User.RoleId.Value) ? rolesDict[tc.User.RoleId.Value] : null
                        } : null
                    }).OrderBy(c => c.createdAt).ToList(),

                    // المرفقات مع بيانات المستخدمين المفصلة
                    attachments = task.Attachments.Select(a => new
                    {
                        id = a.Id,
                        fileName = a.FileName,
                        fileType = a.FileType,
                        fileSize = a.FileSize,
                        fileSizeFormatted = FormatFileSize(a.FileSize),
                        filePath = a.FilePath,
                        uploadedAt = a.UploadedAt,
                        uploadedByUser = a.UploadedByNavigation != null ? new // FIXED
                        {
                            id = a.UploadedByNavigation.Id, // FIXED
                            name = a.UploadedByNavigation.Name, // FIXED
                            email = a.UploadedByNavigation.Email, // FIXED
                            role = a.UploadedByNavigation.RoleId.HasValue && rolesDict.ContainsKey(a.UploadedByNavigation.RoleId.Value) ? rolesDict[a.UploadedByNavigation.RoleId.Value] : null // FIXED
                        } : null
                    }).OrderBy(a => a.uploadedAt).ToList(),

                    // المهام الفرعية مع تفاصيل إضافية
                    subtasks = task.Subtasks.Select(s => new
                    {
                        id = s.Id,
                        title = s.Title,
                        //description = s.,
                        isCompleted = s.IsCompleted,
                        createdAt = s.CreatedAt,
                        completedAt = s.CompletedAt,
                        //priority = s.Priority ?? "عادية",
                        //estimatedTime = s.,
                        //actualTime = s.ActualTime
                    }).OrderBy(s => s.createdAt).ToList(),

                    // تاريخ المهمة (التحويلات والتغييرات) مع تفاصيل شاملة
                    taskHistories = task.TaskHistories.Select(th => new
                    {
                        id = th.Id,
                        action = th.Action,
                        details = th.Details,
                        timestamp = th.Timestamp,
                        changeType = th.ChangeType,
                        changeDescription = th.ChangeDescription,
                        oldValue = th.OldValue,
                        newValue = th.NewValue,
                        changedAt = th.ChangedAt,
                        changedByNavigation = th.ChangedByNavigation != null ? new
                        {
                            id = th.ChangedByNavigation.Id,
                            name = th.ChangedByNavigation.Name,
                            email = th.ChangedByNavigation.Email,
                            role = th.ChangedByNavigation.RoleId.HasValue && rolesDict.ContainsKey(th.ChangedByNavigation.RoleId.Value) ? rolesDict[th.ChangedByNavigation.RoleId.Value] : null
                        } : null,
                        user = new
                        {
                            id = th.User.Id,
                            name = th.User.Name,
                            email = th.User.Email,
                            role = th.User.RoleId.HasValue && rolesDict.ContainsKey(th.User.RoleId.Value) ? rolesDict[th.User.RoleId.Value] : null
                        }
                    }).OrderBy(th => th.timestamp).ToList(),

                    // متتبعات التقدم
                    progressTrackers = taskProgressTrackers,

                    // سجلات تتبع الوقت
                    timeTrackingEntries = timeTrackingEntries,

                    // إحصائيات محسوبة
                    statistics = new
                    {
                        totalComments = task.TaskComments.Count,
                        totalAttachments = task.Attachments.Count,
                        totalSubtasks = task.Subtasks.Count,
                        completedSubtasks = task.Subtasks.Count(s => s.IsCompleted),
                        totalContributors = accessUsers.Count + 
                                          (task.Creator != null ? 1 : 0) + 
                                          (task.Assignee != null && task.Assignee.Id != task.Creator?.Id ? 1 : 0),
                        totalTransfers = task.TaskHistories.Count(th => 
                            th.Action.Contains("assign") || 
                            th.Action.Contains("transfer") || 
                            th.Action.Contains("reassign")),
                        totalActivities = task.TaskHistories.Count,
                        totalTimeTracked = timeTrackingEntries.Sum(tte => tte.duration ?? 0),
                        daysActive = task.CreatedAt > 0 ? 
                            (int)Math.Ceiling((DateTime.Now.ToUniversalTime().Subtract(new DateTime(1970, 1, 1)).TotalSeconds - task.CreatedAt) / 86400.0) : 0,
                        isOverdue = task.DueDate.HasValue && task.Status != "مكتملة" && 
                                   DateTime.Now.ToUniversalTime().Subtract(new DateTime(1970, 1, 1)).TotalSeconds > task.DueDate.Value,
                        completionRate = task.CompletionPercentage,
                        averageProgressUpdate = taskProgressTrackers.Count > 0 ? 
                            taskProgressTrackers.Average(pt => pt.progressPercentage) : task.CompletionPercentage
                    }
                };

                _logger.LogInformation("تم إرجاع البيانات الشاملة للتقرير للمهمة: {TaskId} - {TaskTitle}", id, task.Title);
                _logger.LogInformation("البيانات تحتوي على: {CommentsCount} تعليق، {AttachmentsCount} مرفق، {SubtasksCount} مهمة فرعية، {HistoriesCount} سجل تاريخي", 
                    task.TaskComments.Count, task.Attachments.Count, task.Subtasks.Count, task.TaskHistories.Count);

                // تسجيل عملية إنشاء التقرير الشامل
                var currentUserId = GetCurrentUserId();
                if (currentUserId > 0)
                {
                    await _loggingService.LogActivityAsync(
                        "generate_report",
                        "task",
                        id,
                        currentUserId,
                        $"إنشاء تقرير شامل للمهمة: {task.Title}",
                        taskId: id);
                }

                return Ok(comprehensiveReportData);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على البيانات الشاملة للتقرير للمهمة: {TaskId}", id);
                return StatusCode(500, new
                {
                    success = false,
                    message = "حدث خطأ أثناء جلب البيانات الشاملة للتقرير",
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// تنسيق حجم الملف
        /// </summary>
        /// <param name="bytes">حجم الملف بالبايت</param>
        /// <returns>حجم الملف منسق</returns>
        private static string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB", "TB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }

        /// <summary>
        /// Update a task
        /// </summary>
        /// <param name="id">Task ID</param>
        /// <param name="task">Updated task data</param>
        /// <returns>No content</returns>
        /// <response code="204">Task updated successfully</response>
        /// <response code="400">Invalid request</response>
        /// <response code="404">Task not found</response>
        [HttpPut("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> PutTask(int id, Models.Task task)
        {
            try
            {
                if (id != task.Id)
                {
                    _logger.LogWarning("معرف المهمة في URL ({UrlId}) لا يطابق معرف المهمة في البيانات ({TaskId})", id, task.Id);
                    return BadRequest("معرف المهمة غير متطابق");
                }

                // التحقق من وجود المهمة
                var existingTask = await _context.Tasks.AsNoTracking().FirstOrDefaultAsync(t => t.Id == id);
                if (existingTask == null || existingTask.IsDeleted)
                {
                    _logger.LogWarning("المهمة غير موجودة: {TaskId}", id);
                    return NotFound("المهمة غير موجودة");
                }

                // التحقق من وجود المستخدم المعين إليه (إذا تم تحديده)
                if (task.AssigneeId.HasValue)
                {
                    var assignee = await _context.Users.AsNoTracking().FirstOrDefaultAsync(u => u.Id == task.AssigneeId.Value);
                    if (assignee == null)
                    {
                        _logger.LogWarning("المستخدم المعين إليه غير موجود: {AssigneeId}", task.AssigneeId.Value);
                        return BadRequest($"المستخدم المعين إليه غير موجود: {task.AssigneeId.Value}");
                    }
                }

                // التحقق من وجود القسم (إذا تم تحديده)
                if (task.DepartmentId.HasValue)
                {
                    var department = await _context.Departments.AsNoTracking().FirstOrDefaultAsync(d => d.Id == task.DepartmentId.Value);
                    if (department == null)
                    {
                        _logger.LogWarning("القسم غير موجود: {DepartmentId}", task.DepartmentId.Value);
                        return BadRequest($"القسم غير موجود: {task.DepartmentId.Value}");
                    }
                }

                // التحقق من وجود نوع المهمة (إذا تم تحديده)
                if (task.TaskTypeId.HasValue)
                {
                    var taskType = await _context.TaskTypes.AsNoTracking().FirstOrDefaultAsync(tt => tt.Id == task.TaskTypeId.Value);
                    if (taskType == null)
                    {
                        _logger.LogWarning("نوع المهمة غير موجود: {TaskTypeId}", task.TaskTypeId.Value);
                        return BadRequest($"نوع المهمة غير موجود: {task.TaskTypeId.Value}");
                    }
                }

                _logger.LogInformation("تحديث المهمة: {TaskId} - {TaskTitle}", id, task.Title);
                _logger.LogInformation("بيانات المهمة: Priority={Priority}, Status={Status}, AssigneeId={AssigneeId}",
                    task.Priority, task.Status, task.AssigneeId);

                // فصل أي كيانات متتبعة مسبقاً
                var trackedEntity = _context.ChangeTracker.Entries<Models.Task>()
                    .FirstOrDefault(e => e.Entity.Id == id);
                if (trackedEntity != null)
                {
                    _context.Entry(trackedEntity.Entity).State = EntityState.Detached;
                    _logger.LogInformation("تم فصل الكيان المتتبع مسبقاً");
                }

                _context.Entry(task).State = EntityState.Modified;

                try
                {
                    await _context.SaveChangesAsync();

                    // تحديث وصول المستخدمين للمهمة في جدول task_access_users
                    // الحصول على قائمة المستخدمين الحاليين الذين لديهم صلاحية وصول
                    var existingUserIds = await _context.TaskAccessUsers
                        .Where(tau => tau.TaskId == id)
                        .Select(tau => tau.UserId)
                        .ToListAsync();

                    var usersToAdd = new List<int>();

                    // إضافة المنشئ إذا لم يكن موجوداً
                    if (!existingUserIds.Contains(task.CreatorId))
                    {
                        usersToAdd.Add(task.CreatorId);
                    }

                    // إضافة المسند له إذا موجود ولم يكن موجوداً
                    if (task.AssigneeId.HasValue && !existingUserIds.Contains(task.AssigneeId.Value))
                    {
                        usersToAdd.Add(task.AssigneeId.Value);
                    }

                    // إضافة أي مستخدمين إضافيين من AccessUserIds
                    if (task.AccessUserIds != null && task.AccessUserIds.Any())
                    {
                        foreach (var userIdStr in task.AccessUserIds)
                        {
                            if (int.TryParse(userIdStr, out int userId))
                            {
                                // إضافة المستخدم إذا لم يكن موجوداً
                                if (!existingUserIds.Contains(userId) && !usersToAdd.Contains(userId))
                                {
                                    usersToAdd.Add(userId);
                                }
                            }
                        }
                    }

                    // إضافة المستخدمين الجدد فقط
                    if (usersToAdd.Any())
                    {
                        var newAccessUsers = usersToAdd.Select(userId => new TaskAccessUser
                        {
                            TaskId = id,
                            UserId = userId
                        }).ToList();

                        _context.TaskAccessUsers.AddRange(newAccessUsers);

                        // إرسال إشعارات للمستخدمين الجدد
                        await _notificationService.CreateAndSendNotificationsAsync(
                            usersToAdd,
                            "تمت إضافتك إلى مهمة",
                            $"المهمة رقم #{id}: تمت إضافتك للوصول إلى المهمة '{task?.Title ?? "مهمة"}'",
                            "task_access_granted",
                            id
                        );
                    }
                    await _context.SaveChangesAsync();

                    _logger.LogInformation("تم تحديث المهمة بنجاح: {TaskId}", id);

                    // تسجيل تحديث المهمة في سجلات النشاط
                    var currentUserId = GetCurrentUserId();
                    if (currentUserId > 0)
                    {
                        // تحديد التغييرات
                        var changes = new Dictionary<string, object>();
                        if (existingTask.Title != task.Title)
                        {
                            changes["old_title"] = existingTask.Title ?? "";
                            changes["new_title"] = task.Title ?? "";
                        }
                        if (existingTask.Status != task.Status)
                        {
                            changes["old_status"] = existingTask.Status ?? "";
                            changes["new_status"] = task.Status ?? "";
                        }
                        if (existingTask.Priority != task.Priority)
                        {
                            changes["old_priority"] = existingTask.Priority ?? "";
                            changes["new_priority"] = task.Priority ?? "";
                        }
                        if (existingTask.AssigneeId != task.AssigneeId)
                        {
                            changes["old_assignee_id"] = existingTask.AssigneeId ?? 0;
                            changes["new_assignee_id"] = task.AssigneeId ?? 0;
                        }
                        if (existingTask.CompletionPercentage != task.CompletionPercentage)
                        {
                            changes["old_completion"] = existingTask.CompletionPercentage;
                            changes["new_completion"] = task.CompletionPercentage;
                        }

                        if (changes.Count > 0)
                        {
                            await _loggingService.LogCrudOperationAsync(
                                "UPDATE",
                                "task",
                                id,
                                currentUserId,
                                task.Title,
                                changes,
                                HttpContext.Connection.RemoteIpAddress?.ToString());

                            // إضافة سجل TaskHistory للتغييرات المهمة
                            var now = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

                            // تسجيل تغيير الحالة
                            if (existingTask.Status != task.Status)
                            {
                                var oldStatusArabic = TranslateStatus(existingTask.Status ?? "");
                                var newStatusArabic = TranslateStatus(task.Status ?? "");

                                var statusHistory = new TaskHistory
                                {
                                    TaskId = id,
                                    UserId = currentUserId,
                                    Action = "تغيير_الحالة",
                                    Details = System.Text.Json.JsonSerializer.Serialize(new
                                    {
                                        oldStatus = existingTask.Status ?? "",
                                        newStatus = task.Status ?? ""
                                    }),
                                    Timestamp = now,
                                    ChangeType = "حالة",
                                    ChangeDescription = $"تم تغيير حالة المهمة من '{oldStatusArabic}' إلى '{newStatusArabic}'",
                                    OldValue = oldStatusArabic,
                                    NewValue = newStatusArabic,
                                    ChangedBy = currentUserId,
                                    ChangedAt = now
                                };
                                _context.TaskHistories.Add(statusHistory);
                            }

                            // تسجيل تغيير الأولوية
                            if (existingTask.Priority != task.Priority)
                            {
                                var oldPriorityArabic = TranslatePriority(existingTask.Priority ?? "");
                                var newPriorityArabic = TranslatePriority(task.Priority ?? "");

                                var priorityHistory = new TaskHistory
                                {
                                    TaskId = id,
                                    UserId = currentUserId,
                                    Action = "تغيير_الأولوية",
                                    Details = System.Text.Json.JsonSerializer.Serialize(new
                                    {
                                        oldPriority = existingTask.Priority ?? "",
                                        newPriority = task.Priority ?? ""
                                    }),
                                    Timestamp = now,
                                    ChangeType = "أولوية",
                                    ChangeDescription = $"تم تغيير أولوية المهمة من '{oldPriorityArabic}' إلى '{newPriorityArabic}'",
                                    OldValue = oldPriorityArabic,
                                    NewValue = newPriorityArabic,
                                    ChangedBy = currentUserId,
                                    ChangedAt = now
                                };
                                _context.TaskHistories.Add(priorityHistory);
                            }
                        }
                    }

                    // إعادة تحميل المهمة مع العلاقات
                    var updatedTask = await _context.Tasks
                        .Include(t => t.Creator)
                        .Include(t => t.Assignee)
                        .Include(t => t.Department)
                        .Include(t => t.TaskType)
                        .FirstOrDefaultAsync(t => t.Id == id);

                    // إنشاء إشعارات للمستخدمين المعنيين
                    if (updatedTask != null)
                    {
                        // إنشاء إشعار للمستخدم المسند له إذا تم تغييره
                        if (existingTask.AssigneeId != updatedTask.AssigneeId && updatedTask.AssigneeId.HasValue)
                        {
                            await _notificationService.CreateAndSendNotificationAsync(
                                updatedTask.AssigneeId.Value,
                                "تم تعيين مهمة لك",
                                $"المهمة رقم #{updatedTask.Id}: تم تعيينك للعمل على المهمة '{updatedTask.Title}'",
                                "task_assigned",
                                updatedTask.Id
                            );
                        }

                        // الحصول على معرف المستخدم الذي يقوم بالتحديث
                        var updatingUserId = GetCurrentUserId();

                        // إنشاء إشعار للمستخدمين المعنيين بتحديث المهمة (باستثناء من قام بالتحديث)
                        var taskUsers = await _context.TaskAccessUsers
                            .Where(au => au.TaskId == id && au.UserId != updatingUserId)
                            .Select(au => au.UserId)
                            .ToListAsync();

                        // إضافة منشئ المهمة والمسند له (إذا لم يكونوا من قام بالتحديث)
                        if (updatedTask.CreatorId != updatingUserId && !taskUsers.Contains(updatedTask.CreatorId))
                        {
                            taskUsers.Add(updatedTask.CreatorId);
                        }
                        if (updatedTask.AssigneeId.HasValue && updatedTask.AssigneeId.Value != updatingUserId && !taskUsers.Contains(updatedTask.AssigneeId.Value))
                        {
                            taskUsers.Add(updatedTask.AssigneeId.Value);
                        }

                        if (taskUsers.Any())
                        {
                            await _notificationService.CreateAndSendNotificationsAsync(
                                taskUsers,
                                "تم تحديث مهمة",
                                $"المهمة رقم #{updatedTask.Id}: تم تحديث المهمة '{updatedTask.Title}'",
                                "task_updated",
                                updatedTask.Id
                            );
                        }

                        // إشعارات تغيير الحالة
                        if (existingTask.Status != updatedTask.Status && taskUsers.Any())
                        {
                            await _notificationService.CreateAndSendNotificationsAsync(
                                taskUsers,
                                "تم تغيير حالة المهمة",
                                $"المهمة رقم #{updatedTask.Id}: تم تغيير حالة المهمة '{updatedTask.Title}' إلى '{updatedTask.Status}'",
                                "task_status_changed",
                                updatedTask.Id
                            );
                        }

                        // إشعارات تغيير الأولوية
                        if (existingTask.Priority != updatedTask.Priority && taskUsers.Any())
                        {
                            await _notificationService.CreateAndSendNotificationsAsync(
                                taskUsers,
                                "تم تغيير أولوية المهمة",
                                $"المهمة رقم #{updatedTask.Id}: تم تغيير أولوية المهمة '{updatedTask.Title}' إلى '{updatedTask.Priority}'",
                                "task_priority_changed",
                                updatedTask.Id
                            );
                        }
                    }

                    // After saving changes, broadcast the update via SignalR
                    await _hubContext.Clients.All.SendAsync("TaskUpdated", task);

                    return NoContent();
                }
                catch (DbUpdateException dbEx)
                {
                    _logger.LogError(dbEx, "خطأ في قاعدة البيانات أثناء تحديث المهمة: {TaskId}. Inner Exception: {InnerException}",
                        id, dbEx.InnerException?.Message);
                    return BadRequest($"خطأ في قاعدة البيانات: {dbEx.InnerException?.Message ?? dbEx.Message}");
                }
            }
            catch (DbUpdateConcurrencyException ex)
            {
                if (!TaskExists(id))
                {
                    _logger.LogWarning("المهمة غير موجودة أثناء التحديث: {TaskId}", id);
                    return NotFound();
                }
                else
                {
                    _logger.LogError(ex, "خطأ في التزامن أثناء تحديث المهمة: {TaskId}", id);
                    throw;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث المهمة: {TaskId}", id);
                return BadRequest($"خطأ في تحديث المهمة: {ex.Message}");
            }
        }

        /// <summary>
        /// Create a new task
        /// </summary>
        /// <param name="task">Task data</param>
        /// <returns>Created task</returns>
        /// <response code="201">Task created successfully</response>
        /// <response code="400">Invalid request</response>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<Models.Task>> PostTask(Models.Task task)
        {
            try
            {
                // التحقق من وجود المستخدم المنشئ
                var creator = await _context.Users.FindAsync(task.CreatorId);
                if (creator == null)
                {
                    return BadRequest("المستخدم المنشئ غير موجود");
                }

                // التحقق من وجود المستخدم المعين إليه (إذا تم تحديده)
                if (task.AssigneeId.HasValue)
                {
                    var assignee = await _context.Users.FindAsync(task.AssigneeId.Value);
                    if (assignee == null)
                    {
                        return BadRequest("المستخدم المعين إليه غير موجود");
                    }
                }

                // التحقق من وجود القسم (إذا تم تحديده)
                if (task.DepartmentId.HasValue)
                {
                    var department = await _context.Departments.FindAsync(task.DepartmentId.Value);
                    if (department == null)
                    {
                        return BadRequest("القسم غير موجود");
                    }
                }

                // التحقق من وجود نوع المهمة (إذا تم تحديده)
                if (task.TaskTypeId.HasValue)
                {
                    var taskType = await _context.TaskTypes.FindAsync(task.TaskTypeId.Value);
                    if (taskType == null)
                    {
                        return BadRequest("نوع المهمة غير موجود");
                    }
                }

                // تعيين الوقت الحالي
                task.CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

                // تعيين القيم الافتراضية
                if (task.CompletionPercentage == 0)
                    task.CompletionPercentage = 0;

                task.IsDeleted = false;

                _logger.LogInformation("إنشاء مهمة جديدة: {TaskTitle} بواسطة المستخدم: {CreatorId}",
                    task.Title, task.CreatorId);

                // إضافة المهمة إلى قاعدة البيانات
                _context.Tasks.Add(task);
                await _context.SaveChangesAsync();

                // إضافة المستخدمين الذين لهم وصول للمهمة في جدول task_access_users
                var accessUsers = new List<TaskAccessUser>();

                // إضافة المنشئ
                accessUsers.Add(new TaskAccessUser { TaskId = task.Id, UserId = task.CreatorId });

                // إضافة المسند له إذا موجود
                if (task.AssigneeId.HasValue)
                {
                    accessUsers.Add(new TaskAccessUser { TaskId = task.Id, UserId = task.AssigneeId.Value });
                }

                // إضافة أي مستخدمين إضافيين من AccessUserIds
                if (task.AccessUserIds != null && task.AccessUserIds.Any())
                {
                    foreach (var userIdStr in task.AccessUserIds)
                    {
                        if (int.TryParse(userIdStr, out int userId))
                        {
                            // تجنب التكرار
                            if (!accessUsers.Any(au => au.UserId == userId))
                            {
                                accessUsers.Add(new TaskAccessUser { TaskId = task.Id, UserId = userId });
                            }
                        }
                    }
                }

                _context.TaskAccessUsers.AddRange(accessUsers);
                await _context.SaveChangesAsync();

                // إعادة تحميل المهمة مع العلاقات
                var createdTask = await _context.Tasks
                    .Include(t => t.Creator)
                    .Include(t => t.Assignee)
                    .Include(t => t.Department)
                    .Include(t => t.TaskType)
                    .FirstOrDefaultAsync(t => t.Id == task.Id);

                _logger.LogInformation("تم إنشاء المهمة بنجاح: {TaskId} - {TaskTitle}",
                    createdTask?.Id, createdTask?.Title);

                // تسجيل إنشاء المهمة في سجلات النشاط
                if (createdTask != null)
                {
                    await _loggingService.LogCrudOperationAsync(
                        "CREATE",
                        "task",
                        createdTask.Id,
                        createdTask.CreatorId,
                        createdTask.Title,
                        new Dictionary<string, object>
                        {
                            ["title"] = createdTask.Title ?? "",
                            ["description"] = createdTask.Description ?? "",
                            ["priority"] = createdTask.Priority ?? "",
                            ["status"] = createdTask.Status ?? "",
                            ["assignee_id"] = createdTask.AssigneeId ?? 0,
                            ["department_id"] = createdTask.DepartmentId ?? 0,
                            ["due_date"] = createdTask.DueDate?.ToString() ?? "",
                            ["estimated_time"] = createdTask.EstimatedTime ?? 0
                        },
                        HttpContext.Connection.RemoteIpAddress?.ToString());
                }

                // إنشاء إشعارات للمستخدمين المعنيين
                if (createdTask != null)
                {
                    await CreateTaskNotifications(createdTask);
                }

                return CreatedAtAction("GetTask", new { id = task.Id }, createdTask);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء المهمة: {TaskTitle}", task.Title);
                return BadRequest($"خطأ في إنشاء المهمة: {ex.Message}");
            }
        }

        /// <summary>
        /// Delete a task (soft delete)
        /// </summary>
        /// <param name="id">Task ID</param>
        /// <returns>No content</returns>
        /// <response code="204">Task deleted successfully</response>
        /// <response code="404">Task not found</response>
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteTask(int id)
        {
            var task = await _context.Tasks.FindAsync(id);
            if (task == null)
            {
                return NotFound();
            }

            task.IsDeleted = true;
            await _context.SaveChangesAsync();

            // تسجيل حذف المهمة في سجلات النشاط
            var currentUserId = GetCurrentUserId();
            if (currentUserId > 0)
            {
                await _loggingService.LogCrudOperationAsync(
                    "DELETE",
                    "task",
                    id,
                    currentUserId,
                    task.Title,
                    new Dictionary<string, object>
                    {
                        ["deleted_at"] = DateTimeOffset.UtcNow.ToString(),
                        ["soft_delete"] = true
                    },
                    HttpContext.Connection.RemoteIpAddress?.ToString());
            }

            return NoContent();
        }

        /// <summary>
        /// Get tasks by assignee
        /// </summary>
        /// <param name="assigneeId">Assignee user ID</param>
        /// <returns>List of tasks assigned to the user</returns>
        /// <response code="200">Returns the list of tasks</response>
        [HttpGet("assignee/{assigneeId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<Models.Task>>> GetTasksByAssignee(int assigneeId)
        {
            // تسجيل عملية البحث عن المهام حسب المكلف
            var currentUserId = GetCurrentUserId();
            if (currentUserId > 0)
            {
                await _loggingService.LogActivityAsync(
                    "search_by_assignee",
                    "general",
                    currentUserId,
                    currentUserId,
                    $"البحث عن المهام المكلف بها المستخدم #{assigneeId}");
            }

            return await _context.Tasks
                .Include(t => t.Creator)
                .Include(t => t.Assignee)
                .Include(t => t.Department)
                .Include(t => t.TaskType)
                .Where(t => t.AssigneeId == assigneeId && !t.IsDeleted)
                .ToListAsync();
        }

        /// <summary>
        /// الحصول على المهام التي يمكن للمستخدم الوصول إليها من جدول task_access_users
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>قائمة المهام التي يمكن للمستخدم الوصول إليها</returns>
        /// <response code="200">إرجاع قائمة المهام</response>
        [HttpGet("user-access/{userId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<Models.Task>>> GetTasksByUserAccess(int userId)
        {
            try
            {
                _logger.LogInformation("طلب الحصول على المهام التي يمكن للمستخدم {UserId} الوصول إليها", userId);

                // التحقق من وجود المستخدم
                var userExists = await _context.Users.AnyAsync(u => u.Id == userId);
                if (!userExists)
                {
                    _logger.LogWarning("المستخدم غير موجود: {UserId}", userId);
                    return NotFound("المستخدم غير موجود");
                }

                // جلب المهام التي يمكن للمستخدم الوصول إليها من جدول task_access_users
                // بالإضافة للمهام المسندة إليه والتي أنشأها
                var accessibleTasks = await _context.Tasks
                    .Include(t => t.Creator)
                    .Include(t => t.Assignee)
                    .Include(t => t.Department)
                    .Include(t => t.TaskType)
                    .Where(t => !t.IsDeleted && (
                        // المهام المسندة إليه
                        t.AssigneeId == userId ||
                        // المهام التي أنشأها
                        t.CreatorId == userId ||
                        // المهام التي له صلاحية الوصول إليها من جدول task_access_users
                        _context.TaskAccessUsers.Any(tau => tau.TaskId == t.Id && tau.UserId == userId)
                    ))
                    .OrderByDescending(t => t.CreatedAt)
                    .ToListAsync();

                _logger.LogInformation("تم العثور على {TasksCount} مهمة يمكن للمستخدم {UserId} الوصول إليها",
                    accessibleTasks.Count, userId);

                // تسجيل عملية البحث عن المهام حسب صلاحية الوصول
                var currentUserId = GetCurrentUserId();
                if (currentUserId > 0)
                {
                    await _loggingService.LogActivityAsync(
                        "search_by_access",
                        "task",
                        0,
                        currentUserId,
                        $"البحث عن المهام التي يمكن للمستخدم #{userId} الوصول إليها - تم العثور على {accessibleTasks.Count} مهمة");
                }

                return Ok(accessibleTasks);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على المهام التي يمكن للمستخدم {UserId} الوصول إليها", userId);
                return StatusCode(500, "خطأ في الخادم");
            }
        }

        /// <summary>
        /// Get tasks by status
        /// </summary>
        /// <param name="status">Status ID</param>
        /// <returns>List of tasks with the specified status</returns>
        /// <response code="200">Returns the list of tasks</response>
        [HttpGet("status/{status}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<Models.Task>>> GetTasksByStatus(string status)
        {
            // تسجيل عملية البحث عن المهام حسب الحالة
            var currentUserId = GetCurrentUserId();
            if (currentUserId > 0)
            {
                await _loggingService.LogActivityAsync(
                    "search_by_status",
                    "general",
                    currentUserId,
                    currentUserId,
                    $"البحث عن المهام بحالة: {status}");
            }

            return await _context.Tasks
                .Include(t => t.Creator)
                .Include(t => t.Assignee)
                .Include(t => t.Department)
                .Include(t => t.TaskType)
                .Where(t => t.Status == status && !t.IsDeleted)
                .ToListAsync();
        }

        /// <summary>
        /// Get tasks by priority
        /// </summary>
        /// <param name="priority">Priority level</param>
        /// <returns>List of tasks with the specified priority</returns>
        /// <response code="200">Returns the list of tasks</response>
        [HttpGet("priority/{priority}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<Models.Task>>> GetTasksByPriority(string priority)
        {
            // تسجيل عملية البحث عن المهام حسب الأولوية
            var currentUserId = GetCurrentUserId();
            if (currentUserId > 0)
            {
                await _loggingService.LogActivityAsync(
                    "search_by_priority",
                    "general",
                    currentUserId,
                    currentUserId,
                    $"البحث عن المهام بأولوية: {priority}");
            }

            return await _context.Tasks
                .Include(t => t.Creator)
                .Include(t => t.Assignee)
                .Include(t => t.Department)
                .Include(t => t.TaskType)
                .Where(t => t.Priority == priority && !t.IsDeleted)
                .ToListAsync();
        }

        private bool TaskExists(int id)
        {
            return _context.Tasks.Any(e => e.Id == id && !e.IsDeleted);
        }



        /// <summary>
        /// تطبيق الترتيب على استعلام المهام
        /// </summary>
        /// <param name="query">الاستعلام</param>
        /// <param name="sortField">حقل الترتيب</param>
        /// <param name="sortDirection">اتجاه الترتيب</param>
        /// <returns>الاستعلام مع الترتيب المطبق</returns>
        private IQueryable<Models.Task> ApplyTaskSorting(IQueryable<Models.Task> query, string sortField, string sortDirection)
        {
            var isDescending = sortDirection.ToUpper() == "DESC";

            return sortField.ToLower() switch
            {
                "id" => isDescending ? query.OrderByDescending(t => t.Id) : query.OrderBy(t => t.Id),
                "title" => isDescending ? query.OrderByDescending(t => t.Title) : query.OrderBy(t => t.Title),
                "description" => isDescending ? query.OrderByDescending(t => t.Description) : query.OrderBy(t => t.Description),
                "status" => isDescending ? query.OrderByDescending(t => t.Status) : query.OrderBy(t => t.Status),
                "priority" => isDescending ? query.OrderByDescending(t => t.Priority) : query.OrderBy(t => t.Priority),
                "completionpercentage" => isDescending ? query.OrderByDescending(t => t.CompletionPercentage) : query.OrderBy(t => t.CompletionPercentage),
                "startdate" => isDescending ? query.OrderByDescending(t => t.StartDate) : query.OrderBy(t => t.StartDate),
                "duedate" => isDescending ? query.OrderByDescending(t => t.DueDate) : query.OrderBy(t => t.DueDate),
                "createdat" => isDescending ? query.OrderByDescending(t => t.CreatedAt) : query.OrderBy(t => t.CreatedAt),
                "creatorid" => isDescending ? query.OrderByDescending(t => t.CreatorId) : query.OrderBy(t => t.CreatorId),
                "assigneeid" => isDescending ? query.OrderByDescending(t => t.AssigneeId) : query.OrderBy(t => t.AssigneeId),
                "departmentid" => isDescending ? query.OrderByDescending(t => t.DepartmentId) : query.OrderBy(t => t.DepartmentId),
                "estimatedtime" => isDescending ? query.OrderByDescending(t => t.EstimatedTime) : query.OrderBy(t => t.EstimatedTime),
                _ => query.OrderBy(t => t.Id) // الترتيب الافتراضي
            };
        }

        /// <summary>
        /// تحويل المهمة إلى مستخدم آخر
        /// </summary>
        /// <param name="id">معرف المهمة</param>
        /// <param name="transferRequest">بيانات التحويل</param>
        /// <returns>المهمة بعد التحويل</returns>
        /// <response code="200">تم تحويل المهمة بنجاح</response>
        /// <response code="400">طلب غير صالح</response>
        /// <response code="404">المهمة غير موجودة</response>
        [HttpPost("{id}/transfer")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<Models.Task>> TransferTask(int id, [FromBody] TaskTransferRequest transferRequest)
        {
            try
            {
                // التحقق من وجود المهمة
                var task = await _context.Tasks
                    .Include(t => t.Creator)
                    .Include(t => t.Assignee)
                    .FirstOrDefaultAsync(t => t.Id == id && !t.IsDeleted);

                if (task == null)
                {
                    return NotFound(new { message = "المهمة غير موجودة" });
                }

                // التحقق من وجود المستخدم المستلم
                var newAssignee = await _context.Users.FindAsync(transferRequest.NewAssigneeId);
                if (newAssignee == null)
                {
                    return BadRequest(new { message = "المستخدم المستلم غير موجود" });
                }

                // حفظ المستخدم السابق للتاريخ
                var previousAssigneeId = task.AssigneeId;

                // التحقق من أن التحويل ليس لنفس المستخدم
                if (previousAssigneeId == transferRequest.NewAssigneeId)
                {
                    return BadRequest(new { message = "لا يمكن تحويل المهمة لنفس المستخدم المكلف بها حالياً" });
                }

                // ملاحظة: لا يتم تحديث AssigneeId - المهمة تبقى مرتبطة بالمستخدم الأصلي
                // task.AssigneeId = transferRequest.NewAssigneeId; // تم تعطيل هذا السطر

                // الحصول على أسماء المستخدمين للعرض الواضح
                var previousAssignee = previousAssigneeId.HasValue
                    ? await _context.Users.FindAsync(previousAssigneeId.Value)
                    : null;

                var previousAssigneeName = previousAssignee?.Name ?? "غير مُعيَّن";
                var newAssigneeName = newAssignee?.Name ?? "مستخدم غير معروف";

                // الحصول على أسماء المرفقات من معرفاتها
                var attachmentNames = new List<string>();
                if (transferRequest.Attachments != null && transferRequest.Attachments.Any())
                {
                    foreach (var attachmentIdStr in transferRequest.Attachments)
                    {
                        if (int.TryParse(attachmentIdStr, out int attachmentId))
                        {
                            var attachment = await _context.Attachments
                                .FirstOrDefaultAsync(a => a.Id == attachmentId && !a.IsDeleted);
                            if (attachment != null)
                            {
                                attachmentNames.Add(attachment.FileName);
                            }
                            else
                            {
                                attachmentNames.Add($"مرفق #{attachmentId} (محذوف)");
                            }
                        }
                        else
                        {
                            attachmentNames.Add($"معرف غير صحيح: {attachmentIdStr}");
                        }
                    }
                }

                // إنشاء سجل تاريخ للتحويل مع معلومات مفصلة
                var taskHistory = new TaskHistory
                {
                    TaskId = id,
                    UserId = transferRequest.CurrentUserId,
                    Action = "تحويل_المهمة",
                    Details = System.Text.Json.JsonSerializer.Serialize(new
                    {
                        newAssigneeId = transferRequest.NewAssigneeId.ToString(),
                        newAssigneeName = newAssigneeName,
                        previousAssigneeId = previousAssigneeId?.ToString() ?? "0",
                        previousAssigneeName = previousAssigneeName,
                        transferReason = transferRequest.Comment ?? "لم يتم تحديد سبب",
                        attachments = string.Join(",", attachmentNames),
                        attachmentsCount = attachmentNames.Count,
                        contributionRecorded = "true",
                        contributionPercentage = "5.0"
                    }),
                    Timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                    ChangeType = "تعيين",
                    ChangeDescription = $"تم تحويل المهمة من {previousAssigneeName} إلى {newAssigneeName}",
                    OldValue = previousAssigneeName,
                    NewValue = newAssigneeName,
                    ChangedBy = transferRequest.CurrentUserId,
                    ChangedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
                };

                _context.TaskHistories.Add(taskHistory);

                // لا نضيف تعليق التحويل إلى جدول التعليقات
                // سيتم الاعتماد على task_history فقط لتتبع التحويلات

                // إضافة المرفقات إذا كانت موجودة
                if (transferRequest.Attachments != null && transferRequest.Attachments.Any())
                {
                    foreach (var attachmentId in transferRequest.Attachments)
                    {
                        if (int.TryParse(attachmentId, out int attId))
                        {
                            // التحقق من وجود المرفق
                            var attachment = await _context.Attachments.FindAsync(attId);
                            if (attachment != null)
                            {
                                // ربط المرفق بالمهمة إذا لم يكن مرتبطاً بالفعل
                                if (attachment.TaskId != id)
                                {
                                    attachment.TaskId = id;
                                    _context.Entry(attachment).State = EntityState.Modified;
                                }

                                // إنشاء سجل تاريخ لإضافة المرفق
                                var attachmentHistory = new TaskHistory
                                {
                                    TaskId = id,
                                    UserId = transferRequest.CurrentUserId,
                                    Action = "إضافة_مرفق_تحويل",
                                    Details = System.Text.Json.JsonSerializer.Serialize(new
                                    {
                                        attachmentId = attachmentId,
                                        transferRelated = "true"
                                    }),
                                    Timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                                    ChangeType = "مرفق",
                                    ChangeDescription = "تمت إضافة مرفق أثناء تحويل المهمة",
                                    OldValue = "",
                                    NewValue = attachmentId,
                                    ChangedBy = transferRequest.CurrentUserId,
                                    ChangedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
                                };

                                _context.TaskHistories.Add(attachmentHistory);
                            }
                        }
                    }
                }

                // تسجيل مساهمة للمستخدم الحالي (المرسل)
                var senderProgressTracker = new TaskProgressTracker
                {
                    TaskId = id,
                    UpdatedBy = transferRequest.CurrentUserId,
                    ProgressPercentage = 5.0M, // نسبة مساهمة افتراضية للتحويل
                    Notes = "تم تحويل المهمة إلى مستخدم آخر: " + transferRequest.Comment,
                    UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                    Progress = 5 // تقدم بنسبة 5%
                };

                _context.TaskProgressTrackers.Add(senderProgressTracker);

                // تسجيل مساهمة للمستخدم الجديد (المستقبل)
                var receiverProgressTracker = new TaskProgressTracker
                {
                    TaskId = id,
                    UpdatedBy = transferRequest.NewAssigneeId,
                    ProgressPercentage = 0.0M, // بداية العمل على المهمة
                    Notes = "تم استلام المهمة من مستخدم آخر",
                    UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                    Progress = 0 // بداية العمل
                };

                _context.TaskProgressTrackers.Add(receiverProgressTracker);

                // تحديث جدول task_access_users - إضافة المستخدمين الجدد بدون حذف الموجودين
                // ملاحظة: لا يتم تحديث assignee_id - المهمة تبقى مرتبطة بالمستخدم الأصلي
                // الحصول على قائمة المستخدمين الحاليين الذين لديهم صلاحية وصول
                var existingUserIds = await _context.TaskAccessUsers
                    .Where(tau => tau.TaskId == id)
                    .Select(tau => tau.UserId)
                    .ToListAsync();

                var usersToAdd = new List<int>();

                // إضافة المنشئ إذا لم يكن موجوداً
                if (!existingUserIds.Contains(task.CreatorId))
                {
                    usersToAdd.Add(task.CreatorId);
                }

                // إضافة المستخدم الجديد (المحول إليه) إذا لم يكن موجوداً
                if (!existingUserIds.Contains(transferRequest.NewAssigneeId))
                {
                    usersToAdd.Add(transferRequest.NewAssigneeId);
                }

                // إضافة المستخدم المرسل إذا لم يكن موجوداً وليس هو المنشئ
                if (transferRequest.CurrentUserId != task.CreatorId &&
                    !existingUserIds.Contains(transferRequest.CurrentUserId))
                {
                    usersToAdd.Add(transferRequest.CurrentUserId);
                }

                // إضافة المستخدمين الجدد فقط
                if (usersToAdd.Any())
                {
                    var newAccessUsers = usersToAdd.Select(userId => new TaskAccessUser
                    {
                        TaskId = id,
                        UserId = userId
                    }).ToList();

                    _context.TaskAccessUsers.AddRange(newAccessUsers);

                    // إرسال إشعارات للمستخدمين الجدد
                    await _notificationService.CreateAndSendNotificationsAsync(
                        usersToAdd,
                        "تمت إضافتك إلى مهمة",
                        $"تمت إضافتك للوصول إلى المهمة: {task.Title}",
                        "task_access_granted",
                        id
                    );
                }

                // حفظ التغييرات
                await _context.SaveChangesAsync();

                // تسجيل تحويل المهمة في سجلات النشاط
                await _loggingService.LogCrudOperationAsync(
                    "UPDATE",
                    "task",
                    id,
                    transferRequest.CurrentUserId,
                    task.Title,
                    new Dictionary<string, object>
                    {
                        ["action"] = "transfer",
                        ["old_assignee_id"] = previousAssigneeId ?? 0,
                        ["new_assignee_id"] = transferRequest.NewAssigneeId,
                        ["transfer_comment"] = transferRequest.Comment ?? "",
                        ["attachments_count"] = transferRequest.Attachments?.Count ?? 0
                    },
                    HttpContext.Connection.RemoteIpAddress?.ToString());

                // إرسال إشعار نظامي (NotificationService) وتخزينه في قاعدة البيانات + SignalR
                await _notificationService.CreateAndSendNotificationAsync(
                    transferRequest.NewAssigneeId,
                    "تم تحويل مهمة إليك",
                    $"المهمة رقم #{task.Id}: تم تحويل المهمة '{task.Title}' إليك من مستخدم آخر",
                    "task_transferred",
                    task.Id
                );

                // إرسال إشعار عبر SignalR (يدوي - يمكن حذفه إذا لم يعد هناك حاجة)
                await _hubContext.Clients.User(transferRequest.NewAssigneeId.ToString()).SendAsync(
                    "ReceiveTaskNotification",
                    new
                    {
                        type = "task_transfer",
                        taskId = id,
                        title = "تم تحويل مهمة إليك",
                        message = $"تم تحويل المهمة \"{task.Title}\" إليك",
                        fromUserId = transferRequest.CurrentUserId,
                        timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
                    }
                );

                // إعادة تحميل المهمة مع البيانات المرتبطة
                task = await _context.Tasks
                    .Include(t => t.Creator)
                    .Include(t => t.Assignee)
                    .Include(t => t.Department)
                    .Include(t => t.TaskType)
                    .Include(t => t.Subtasks)
                    .Include(t => t.TaskComments)
                    .Include(t => t.Attachments)
                    .FirstOrDefaultAsync(t => t.Id == id);

                return Ok(task);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "حدث خطأ أثناء تحويل المهمة",
                    error = ex.Message,
                    details = ex.InnerException?.Message
                });
            }
        }

        /// <summary>
        /// إنشاء إشعارات للمستخدمين المعنيين بالمهمة
        /// </summary>
        /// <param name="task">المهمة التي تم إنشاؤها أو تحديثها</param>
        private async System.Threading.Tasks.Task CreateTaskNotifications(webApi.Models.Task task)
        {
            try
            {
                if (task == null)
                {
                    _logger.LogWarning("محاولة إنشاء إشعارات لمهمة فارغة");
                    return;
                }

                // قائمة المستخدمين الذين سيتلقون إشعارات
                var userIds = new List<int>();

                // إضافة المسند له إذا موجود
                if (task.AssigneeId.HasValue && task.AssigneeId.Value != task.CreatorId)
                {
                    userIds.Add(task.AssigneeId.Value);

                    // إنشاء إشعار للمستخدم المسند له
                    await _notificationService.CreateAndSendNotificationAsync(
                        task.AssigneeId.Value,
                        "تم تعيين مهمة جديدة لك",
                        $"المهمة رقم #{task.Id}: تم تعيينك للعمل على المهمة '{task.Title}'",
                        "task_assigned",
                        task.Id
                    );
                }

                // الحصول على المستخدمين الذين لهم وصول للمهمة
                var accessUsers = await _context.TaskAccessUsers
                    .Where(au => au.TaskId == task.Id && au.UserId != task.CreatorId && (!task.AssigneeId.HasValue || au.UserId != task.AssigneeId.Value))
                    .Select(au => au.UserId)
                    .ToListAsync();

                // إضافة المستخدمين الذين لهم وصول للمهمة
                userIds.AddRange(accessUsers);

                // إنشاء إشعارات للمستخدمين الذين لهم وصول للمهمة
                if (accessUsers.Any())
                {
                    await _notificationService.CreateAndSendNotificationsAsync(
                        accessUsers,
                        "تمت إضافتك إلى مهمة جديدة",
                        $"المهمة رقم #{task.Id}: تمت إضافتك للوصول إلى المهمة '{task.Title}'",
                        "added_to_task",
                        task.Id
                    );
                }

                _logger.LogInformation("تم إنشاء إشعارات لـ {Count} مستخدم للمهمة {TaskId}", userIds.Count, task.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء إشعارات للمهمة {TaskId}", task?.Id);
            }
        }

        /// <summary>
        /// الحصول على المهام المتأخرة
        /// </summary>
        /// <returns>قائمة المهام المتأخرة</returns>
        /// <response code="200">إرجاع قائمة المهام المتأخرة</response>
        [HttpGet("overdue")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<Models.Task>>> GetOverdueTasks()
        {
            try
            {
                var currentTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

                var overdueTasks = await _context.Tasks
                    .Include(t => t.Creator)
                    .Include(t => t.Assignee)
                    .Include(t => t.Department)
                    .Include(t => t.TaskType)
                    .Where(t => t.DueDate.HasValue &&
                               t.Status != "مكتملة" &&
                               t.Status != "ملغية" &&
                               !t.IsDeleted &&
                               t.DueDate.Value < currentTime)
                    .OrderBy(t => t.DueDate)
                    .ToListAsync();

                // تسجيل عملية البحث عن المهام المتأخرة
                var currentUserId = GetCurrentUserId();
                if (currentUserId > 0)
                {
                    await _loggingService.LogActivityAsync(
                        "search_overdue",
                        "task",
                        0,
                        currentUserId,
                        $"البحث عن المهام المتأخرة - تم العثور على {overdueTasks.Count} مهمة");
                }

                return Ok(overdueTasks);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على المهام المتأخرة");
                return StatusCode(500, "خطأ في الخادم");
            }
        }

        /// <summary>
        /// الحصول على المهام المقتربة من الموعد النهائي (48 ساعة أو أقل)
        /// </summary>
        /// <returns>قائمة المهام المقتربة من الموعد النهائي</returns>
        /// <response code="200">إرجاع قائمة المهام المقتربة</response>
        [HttpGet("due-soon")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<Models.Task>>> GetTasksDueSoon()
        {
            try
            {
                var currentTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                var fortyEightHoursFromNow = currentTime + (48 * 60 * 60);

                var dueSoonTasks = await _context.Tasks
                    .Include(t => t.Creator)
                    .Include(t => t.Assignee)
                    .Include(t => t.Department)
                    .Include(t => t.TaskType)
                    .Where(t => t.DueDate.HasValue &&
                               t.Status != "مكتملة" &&
                               t.Status != "ملغية" &&
                               !t.IsDeleted &&
                               t.DueDate.Value <= fortyEightHoursFromNow &&
                               t.DueDate.Value > currentTime)
                    .OrderBy(t => t.DueDate)
                    .ToListAsync();

                // تسجيل عملية البحث عن المهام المقتربة من الموعد النهائي
                var currentUserId = GetCurrentUserId();
                if (currentUserId > 0)
                {
                    await _loggingService.LogActivityAsync(
                        "search_due_soon",
                        "task",
                        0,
                        currentUserId,
                        $"البحث عن المهام المقتربة من الموعد النهائي - تم العثور على {dueSoonTasks.Count} مهمة");
                }

                return Ok(dueSoonTasks);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على المهام المقتربة من الموعد النهائي");
                return StatusCode(500, "خطأ في الخادم");
            }
        }

        /// <summary>
        /// الحصول على معرف المستخدم الحالي
        /// </summary>
        private int GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst("UserId")?.Value;
            if (int.TryParse(userIdClaim, out int userId))
            {
                return userId;
            }

            // للاختبار فقط - إرجاع المستخدم الأول
            return 1;
        }

        /// <summary>
        /// ترجمة حالة المهمة إلى العربية
        /// </summary>
        private static string TranslateStatus(string status)
        {
            return status.ToLower() switch
            {
                "pending" => "قيد الانتظار",
                "in_progress" => "قيد التنفيذ",
                "inprogress" => "قيد التنفيذ",
                "completed" => "مكتملة",
                "cancelled" => "ملغية",
                "waiting_for_info" => "في انتظار معلومات",
                _ => status
            };
        }

        /// <summary>
        /// ترجمة أولوية المهمة إلى العربية
        /// </summary>
        private static string TranslatePriority(string priority)
        {
            return priority.ToLower() switch
            {
                "low" => "منخفضة",
                "medium" => "متوسطة",
                "high" => "عالية",
                "urgent" => "عاجلة",
                _ => priority
            };
        }
    }
}

/// <summary>
/// نموذج طلب تحويل المهمة
/// </summary>
public class TaskTransferRequest
{
    /// <summary>
    /// معرف المستخدم المستلم الجديد
    /// </summary>
    public int NewAssigneeId { get; set; }

    /// <summary>
    /// معرف المستخدم الحالي (المرسل)
    /// </summary>
    public int CurrentUserId { get; set; }

    /// <summary>
    /// تعليق التحويل
    /// </summary>
    public string Comment { get; set; } = string.Empty;

    /// <summary>
    /// قائمة معرفات المرفقات
    /// </summary>
    public List<string>? Attachments { get; set; }
}
